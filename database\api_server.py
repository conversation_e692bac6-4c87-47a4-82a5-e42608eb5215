#!/usr/bin/env python3
"""
医疗专长地图 - 简化API服务器
"""
import os
import sys
from flask import Flask, request, jsonify, session
from flask_cors import CORS
from datetime import datetime

# 设置控制台编码为UTF-8 (必须在最开始)
import sys
import os
if sys.platform == 'win32':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# 导入数据库配置 - 简化导入逻辑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from config import db
    from ai_chat_service import ai_chat_service
    print("✅ 数据库配置导入成功")
    print("✅ AI聊天服务导入成功")
except ImportError as e:
    print(f"❌ 导入配置失败: {e}")
    sys.exit(1)

# 创建Flask应用
app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'medical-map-secret-key-2024')

# 启用CORS
CORS(app, supports_credentials=True, origins=['http://localhost:8000'])

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'success': True,
        'service': '医疗专长地图API',
        'status': 'running',
        'database': 'SQLite',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        user_type = data.get('userType', 'patient')
        
        if not all([username, email, password]):
            return jsonify({'success': False, 'message': '请填写所有必填字段'}), 400
        
        result = db.create_user(username, email, password, user_type)
        
        if result['success']:
            return jsonify(result), 201
        else:
            return jsonify(result), 400
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        
        if not all([email, password]):
            return jsonify({'success': False, 'message': '请输入邮箱和密码'}), 400
        
        result = db.authenticate_user(email, password)
        
        if result['success']:
            # 创建会话
            session_result = db.create_session(result['user']['id'])
            if session_result['success']:
                session['session_token'] = session_result['session_token']
                return jsonify({
                    'success': True,
                    'message': '登录成功',
                    'user': result['user']
                })
            else:
                return jsonify({'success': False, 'message': '创建会话失败'}), 500
        else:
            return jsonify(result), 401
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/auth/me', methods=['GET'])
def get_current_user():
    """获取当前用户信息"""
    try:
        session_token = session.get('session_token')
        if not session_token:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        user = db.get_user_by_session(session_token)
        if user:
            return jsonify({'success': True, 'user': user})
        else:
            return jsonify({'success': False, 'message': '会话已过期'}), 401
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/auth/logout', methods=['POST'])
def logout():
    """用户登出"""
    session.clear()
    return jsonify({'success': True, 'message': '已登出'})

# 聊天相关接口
@app.route('/api/chat/messages', methods=['GET'])
def get_chat_messages():
    """获取聊天消息"""
    try:
        limit = request.args.get('limit', 30, type=int)
        
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT username, user_type, avatar_color, avatar_initial, message, created_at
            FROM chat_messages 
            ORDER BY created_at DESC 
            LIMIT ?
        """, (limit,))
        
        messages = []
        for row in cursor.fetchall():
            messages.append({
                'username': row['username'],
                'user_type': row['user_type'],
                'avatar_color': row['avatar_color'],
                'avatar_initial': row['avatar_initial'],
                'message': row['message'],
                'created_at': row['created_at']
            })
        
        # 按时间正序返回
        messages.reverse()
        
        conn.close()
        return jsonify({'success': True, 'messages': messages})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取消息失败: {str(e)}'}), 500

@app.route('/api/chat/online', methods=['GET'])
def get_online_users():
    """获取在线用户"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # 获取5分钟内活跃的用户
        cursor.execute("""
            SELECT username, user_type, avatar_color, avatar_initial
            FROM chat_online_users 
            WHERE last_seen > datetime('now', '-5 minutes')
            ORDER BY last_seen DESC
        """)
        
        users = []
        for row in cursor.fetchall():
            users.append({
                'username': row['username'],
                'user_type': row['user_type'],
                'avatar_color': row['avatar_color'],
                'avatar_initial': row['avatar_initial']
            })
        
        conn.close()
        return jsonify({'success': True, 'users': users})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取在线用户失败: {str(e)}'}), 500

@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    """发送聊天消息"""
    try:
        # 检查用户登录状态
        session_token = session.get('session_token')
        if not session_token:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        user = db.get_user_by_session(session_token)
        if not user:
            return jsonify({'success': False, 'message': '会话已过期'}), 401
        
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'success': False, 'message': '消息不能为空'}), 400
        
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # 插入用户消息
        cursor.execute("""
            INSERT INTO chat_messages 
            (user_id, username, user_type, avatar_color, avatar_initial, message)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (user['id'], user['username'], user['user_type'], 
              user['avatar_color'], user['avatar_initial'], message))
        
        # 更新在线状态
        cursor.execute("""
            INSERT OR REPLACE INTO chat_online_users 
            (user_id, username, user_type, avatar_color, avatar_initial, last_seen)
            VALUES (?, ?, ?, ?, ?, datetime('now'))
        """, (user['id'], user['username'], user['user_type'], 
              user['avatar_color'], user['avatar_initial']))
        
        conn.commit()
        
        # 检查是否需要AI回复
        should_ai_reply = ai_chat_service.is_ai_trigger(message)
        ai_response = None
        
        if should_ai_reply:
            try:
                # 获取最近的对话历史
                cursor.execute("""
                    SELECT username, message, created_at
                    FROM chat_messages 
                    ORDER BY created_at DESC 
                    LIMIT 10
                """)
                
                history = []
                for row in cursor.fetchall():
                    history.append({
                        'username': row['username'],
                        'message': row['message'],
                        'created_at': row['created_at']
                    })
                
                history.reverse()  # 按时间正序
                
                # 调用AI聊天服务（同步版本）
                ai_response = ai_chat_service.chat_with_ai(message, history)
                
                # 插入AI回复消息
                if ai_response:
                    cursor.execute("""
                        INSERT INTO chat_messages 
                        (user_id, username, user_type, avatar_color, avatar_initial, message)
                        VALUES (NULL, ?, ?, ?, ?, ?)
                    """, ('医疗小助手', 'ai_assistant', '#4CAF50', '🤖', ai_response))
                    
                    conn.commit()
                    
            except Exception as e:
                print(f"AI回复失败: {e}")
                # AI回复失败时的备用回复
                cursor.execute("""
                    INSERT INTO chat_messages 
                    (user_id, username, user_type, avatar_color, avatar_initial, message)
                    VALUES (NULL, ?, ?, ?, ?, ?)
                """, ('医疗小助手', 'ai_assistant', '#4CAF50', '🤖', 
                      '抱歉，我暂时无法回应，请稍后再试或联系人工客服。'))
                conn.commit()
        
        conn.close()
        
        response_data = {'success': True, 'message': '消息发送成功'}
        if ai_response:
            response_data['ai_replied'] = True
            response_data['ai_response'] = ai_response
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'发送消息失败: {str(e)}'}), 500

if __name__ == '__main__':
    port = int(os.getenv('API_PORT', 5000))
    
    try:
        print(f"🚀 启动API服务器 (端口: {port})")
        print(f"🗄️  数据库: SQLite ({db.db_path})")
    except UnicodeEncodeError:
        print(f"启动API服务器 (端口: {port})")
        print(f"数据库: SQLite ({db.db_path})")
    
    app.run(host='0.0.0.0', port=port, debug=False)