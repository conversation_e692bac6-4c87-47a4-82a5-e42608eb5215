// 调试版AuthManager - 增加详细日志
class DebugAuthManager {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.users = this.loadUsers();
        this.sessionTimeout = 24 * 60 * 60 * 1000;

        // 数据库API配置
        this.useDatabase = true;
        this.apiBaseUrl = 'http://localhost:5000/api';
        
        console.log('🔍 DebugAuthManager 初始化');
        console.log('  useDatabase:', this.useDatabase);
        console.log('  apiBaseUrl:', this.apiBaseUrl);

        this.init();
    }

    init() {
        this.checkExistingSession();
    }

    checkExistingSession() {
        const savedSession = localStorage.getItem('medicalMapSession');
        if (savedSession) {
            try {
                const session = JSON.parse(savedSession);
                const now = Date.now();
                
                if (session.expiry > now) {
                    this.currentUser = session.user;
                    this.isLoggedIn = true;
                    console.log('✅ 恢复已保存的会话:', this.currentUser.username);
                } else {
                    console.log('⏰ 会话已过期，清理中...');
                    this.logout();
                }
            } catch (error) {
                console.error('❌ 解析会话数据失败:', error);
                this.logout();
            }
        }
    }

    loadUsers() {
        // 返回空数组，强制使用API
        return [];
    }

    async login(email, password, remember = false) {
        console.log('🔐 开始登录流程');
        console.log('  邮箱:', email);
        console.log('  使用数据库:', this.useDatabase);
        
        if (!email || !password) {
            return { success: false, message: '请输入邮箱和密码' };
        }

        // 强制使用API登录
        if (this.useDatabase) {
            console.log('📡 尝试API登录...');
            try {
                const result = await this.loginWithAPI(email, password, remember);
                console.log('📡 API登录结果:', result);
                return result;
            } catch (error) {
                console.error('❌ API登录异常:', error);
                return { success: false, message: `API登录失败: ${error.message}` };
            }
        }

        return { success: false, message: '数据库未启用' };
    }

    async loginWithAPI(email, password, remember = false) {
        console.log('📡 发送API登录请求...');
        console.log('  URL:', `${this.apiBaseUrl}/auth/login`);
        console.log('  数据:', { email, password: '***' });
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            });

            console.log('📡 API响应状态:', response.status);
            console.log('📡 API响应头:', [...response.headers.entries()]);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('📡 API响应数据:', result);

            if (result.success) {
                console.log('✅ API登录成功');
                
                this.currentUser = result.user;
                this.isLoggedIn = true;

                const sessionData = {
                    user: this.currentUser,
                    expiry: Date.now() + (remember ? this.sessionTimeout * 7 : this.sessionTimeout)
                };
                localStorage.setItem('medicalMapSession', JSON.stringify(sessionData));

                if (window.chatManager) {
                    window.chatManager.updateUser(this.currentUser);
                }

                return {
                    success: true,
                    message: '登录成功！',
                    user: this.currentUser,
                    redirect: 'index.html'
                };
            } else {
                console.log('❌ API登录失败:', result.message);
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('❌ API请求失败:', error);
            throw error;
        }
    }

    logout() {
        console.log('🚪 用户登出');
        this.currentUser = null;
        this.isLoggedIn = false;
        localStorage.removeItem('medicalMapSession');
        
        if (window.location.pathname !== '/login.html') {
            window.location.href = 'login.html';
        }
    }

    isAuthenticated() {
        return this.isLoggedIn && this.currentUser;
    }

    getCurrentUser() {
        return this.currentUser;
    }
}