/* 认证页面样式 - 基于极致紧凑布局优化 */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.auth-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 5px;
    overflow: hidden;
    box-sizing: border-box;
}

.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-medical-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.medical-icon {
    position: absolute;
    font-size: 2rem;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.medical-icon:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.medical-icon:nth-child(2) {
    top: 20%;
    right: 10%;
    animation-delay: 1s;
}

.medical-icon:nth-child(3) {
    top: 70%;
    left: 5%;
    animation-delay: 2s;
}

.medical-icon:nth-child(4) {
    bottom: 20%;
    right: 15%;
    animation-delay: 3s;
}

.medical-icon:nth-child(5) {
    top: 50%;
    left: 15%;
    animation-delay: 4s;
}

.medical-icon:nth-child(6) {
    bottom: 10%;
    left: 50%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(10deg); }
}

.auth-card {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 20px;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 15px 35px rgba(102, 126, 234, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    padding: 32px 28px;
    width: 100%;
    max-width: 380px;
    max-height: 98vh;
    overflow-y: auto;
    position: relative;
    z-index: 2;
    animation: slideUp 0.6s ease-out;
    box-sizing: border-box;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #667eea transparent;
}

.auth-card::-webkit-scrollbar {
    width: 4px;
}

.auth-card::-webkit-scrollbar-track {
    background: transparent;
}

.auth-card::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 2px;
}

.auth-card::-webkit-scrollbar-thumb:hover {
    background: #5a67d8;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-logo {
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(102, 126, 234, 0.08);
}

.logo-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 12px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 4px;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.logo-icon svg {
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.auth-logo h1 {
    font-size: 1.6rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2px;
    line-height: 1.2;
}

.auth-logo p {
    color: #64748b;
    font-size: 0.85rem;
    margin: 0;
    line-height: 1.4;
    font-weight: 500;
}

.auth-tabs {
    display: flex;
    margin-bottom: 20px;
    background: rgba(102, 126, 234, 0.06);
    border-radius: 12px;
    padding: 6px;
    position: relative;
}

.auth-tab {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: #64748b;
    font-weight: 600;
    font-size: 0.9rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.auth-tab.active {
    background: white;
    color: #667eea;
    box-shadow: 
        0 4px 12px rgba(102, 126, 234, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.auth-form {
    transition: all 0.3s ease;
}

.auth-form.hidden {
    display: none;
}

.form-group {
    position: relative;
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #374151;
    font-weight: 600;
    font-size: 0.85rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #fafbfc;
    box-sizing: border-box;
    font-weight: 500;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 
        0 0 0 3px rgba(102, 126, 234, 0.08),
        0 4px 12px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.form-group input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.form-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.1rem;
    color: #999;
}

.form-group label + input + .form-icon {
    top: calc(50% + 12px);
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.form-group label + input + .form-icon + .password-toggle {
    top: calc(50% + 12px);
}

.password-toggle:hover {
    background: rgba(102, 126, 234, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    font-size: 0.85rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #64748b;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    transform: scale(1.1);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: "✓";
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password,
.terms-link {
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.forgot-password:hover,
.terms-link:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.auth-button {
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 16px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.auth-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: left 0.5s;
}

.auth-button:hover::before {
    left: 100%;
}

.auth-button:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.35);
}

.auth-button:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.auth-divider {
    text-align: center;
    margin: 4px 0;
    position: relative;
    color: #999;
    font-size: 0.6rem;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e5e5;
    z-index: 1;
}

.auth-divider span {
    background: white;
    padding: 0 16px;
    position: relative;
    z-index: 2;
}

.social-login {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.social-btn {
    flex: 1;
    padding: 8px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 0.8rem;
    font-weight: 500;
}

.social-btn:hover {
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.social-btn.wechat:hover {
    border-color: #07c160;
    color: #07c160;
}

.social-btn.qq:hover {
    border-color: #12b7f5;
    color: #12b7f5;
}

.social-icon {
    font-size: 1.2rem;
}

.auth-footer {
    text-align: center;
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
}

.auth-footer p {
    margin-bottom: 4px;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

.back-to-home {
    text-align: center;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
}

.back-to-home a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    font-size: 0.8rem;
}

.back-to-home a:hover {
    color: #5a67d8;
}

/* 响应式设计 - 针对极致紧凑布局优化 */
@media (max-width: 480px) {
    .auth-container {
        padding: 2px;
    }
    
    .auth-card {
        padding: 15px;
        max-width: 100%;
        border-radius: 8px;
    }
    
    .auth-logo h1 {
        font-size: 1.2rem;
    }
    
    .auth-logo p {
        font-size: 0.75rem;
    }
    
    .social-login {
        flex-direction: column;
        gap: 6px;
    }
    
    .form-options {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
    
    .form-group input,
    .form-group select {
        padding: 8px 30px 8px 10px;
        font-size: 0.85rem;
    }
    
    .auth-button {
        padding: 9px;
        font-size: 0.9rem;
    }
    
    .social-btn {
        padding: 10px;
        font-size: 0.85rem;
    }
}

@media (max-height: 600px) {
    .auth-card {
        max-height: 99vh;
        padding: 12px;
    }
    
    .auth-logo {
        margin-bottom: 8px;
    }
    
    .auth-logo h1 {
        font-size: 1.2rem;
        margin-bottom: 2px;
    }
    
    .auth-logo p {
        margin-top: 2px;
        font-size: 0.75rem;
    }
    
    .form-group {
        margin-bottom: 6px;
    }
    
    .form-group label {
        margin-bottom: 2px;
        font-size: 0.8rem;
    }
}

@media (max-height: 500px) {
    .auth-container {
        align-items: flex-start;
        padding-top: 5px;
    }
    
    .auth-card {
        margin-top: 0;
        max-height: 100vh;
        padding: 10px;
    }
    
    .logo-icon {
        width: 30px;
        height: 30px;
        margin-bottom: 4px;
    }
    
    .auth-logo h1 {
        font-size: 1.1rem;
    }
    
    .back-to-home {
        margin-top: 5px;
        padding-top: 4px;
    }
}

/* 表单验证样式 */
.form-group.error input {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-group.success input {
    border-color: #27ae60;
    box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

.error-message {
    color: #e74c3c;
    font-size: 0.8rem;
    margin-top: 4px;
    display: none;
}

.form-group.error .error-message {
    display: block;
}

/* 加载状态 */
.auth-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.auth-button.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 密码强度指示器 */
.password-strength {
    margin-top: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.password-strength.weak {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.password-strength.medium {
    background: #fff3e0;
    color: #ef6c00;
    border: 1px solid #ffcc02;
}

.password-strength.strong {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

.password-strength.very-strong {
    background: #e3f2fd;
    color: #1565c0;
    border: 1px solid #90caf9;
}

/* 字段错误提示 */
.field-error {
    color: #e53e3e;
    font-size: 12px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.field-error::before {
    content: '⚠️';
    font-size: 14px;
}

.form-group input.error,
.form-group select.error {
    border-color: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

/* Toast 消息 */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    z-index: 10000;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
    max-width: 400px;
}

.toast-message.show {
    transform: translateX(0);
}

.toast-message.hide {
    transform: translateX(100%);
    opacity: 0;
}

.toast-message.success {
    border-left-color: #4caf50;
}

.toast-message.error {
    border-left-color: #f44336;
}

.toast-message.warning {
    border-left-color: #ff9800;
}

.toast-message.info {
    border-left-color: #2196f3;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast-icon {
    font-size: 18px;
}

.toast-text {
    color: #333;
    font-weight: 500;
}

/* 加载状态增强 */
.auth-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    background: #ccc;
}

.auth-button:disabled:hover {
    background: #ccc;
    transform: none;
}

/* 自定义选择框样式 */
.form-group select {
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="m0,1 h4 l-2,3 z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px;
    appearance: none;
}

/* 增强的交互效果 */
.form-group input:focus + .form-icon,
.form-group select:focus + .form-icon {
    transform: scale(1.1);
    filter: saturate(1.5);
}

/* 响应式Toast消息 */
@media (max-width: 480px) {
    .toast-message {
        left: 10px;
        right: 10px;
        top: 10px;
        transform: translateY(-100%);
    }
    
    .toast-message.show {
        transform: translateY(0);
    }
    
    .toast-message.hide {
        transform: translateY(-100%);
    }
}