// 主应用程序
class MedicalMapApp {
    constructor() {
        this.dataManager = null;
        this.mapManager = null;
        this.uiManager = null;
        this.authManager = null;
    }

    async init() {
        try {
            // 初始化认证管理器
            this.authManager = new AuthManager();
            
            // 检查登录状态
            if (!this.authManager.isAuthenticated()) {
                // 未登录，重定向到登录页面
                window.location.href = 'login.html';
                return;
            }
            
            // 初始化用户界面
            this.initUserInterface();
            
            // 显示加载状态
            document.getElementById('map').innerHTML = '<div class="loading">正在加载医院数据...</div>';
            
            // 初始化数据管理器
            this.dataManager = new DataManager();
            await this.dataManager.loadCSVData();
            
            console.log('成功加载', this.dataManager.getAllData().length, '条医院数据');
            
            // 检查是否有数据
            if (this.dataManager.getAllData().length === 0) {
                this.showNoDataMessage();
                return;
            }
            
            // 初始化地图管理器
            this.mapManager = new MapManager(this.dataManager);
            
            // 初始化UI管理器
            this.uiManager = new UIManager(this.dataManager, this.mapManager);
            
            // 加载用户收藏
            this.uiManager.loadBookmarks();
            
            // 初始化界面
            this.uiManager.updateHospitalList();
            this.uiManager.updateStats();
            this.uiManager.updateFilterButtons();
            this.uiManager.setupEventListeners();
            
            // 初始化地图
            if (typeof google !== 'undefined') {
                this.mapManager.initMap();
            } else {
                this.showMapLoadingMessage();
            }
            
            // 处理URL参数
            this.handleURLParams();
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showErrorMessage(error.message);
        }
    }

    initUserInterface() {
        // 添加用户状态显示到头部
        this.addUserStatusToHeader();
        
        // 设置用户相关事件监听器
        this.setupUserEventListeners();
        
        // 如果用户已登录，显示欢迎信息
        if (this.authManager && this.authManager.isAuthenticated()) {
            this.showWelcomeMessage();
        }
    }

    addUserStatusToHeader() {
        const userStatus = document.getElementById('userStatus');
        if (!userStatus) return;

        const isAuthenticated = this.authManager && this.authManager.isAuthenticated();

        if (!isAuthenticated) {
            userStatus.innerHTML = this.getLoginLinksHTML();
            return;
        }

        userStatus.innerHTML = `
            <div class="user-info" id="userInfo">
                ${this.getUserInfoHTML()}
            </div>
            <div class="user-menu" id="userMenu">
                <div class="user-menu-content">
                    <a href="profile.html">
                        <span class="menu-icon">👤</span>个人中心
                    </a>
                    <a href="#" onclick="app.showSettings()">
                        <span class="menu-icon">⚙️</span>设置
                    </a>
                    <a href="#" onclick="app.showHelp()">
                        <span class="menu-icon">❓</span>帮助
                    </a>
                    <div class="menu-divider"></div>
                    <a href="#" onclick="app.authManager.logout()" class="logout-btn">
                        <span class="menu-icon">🚪</span>退出登录
                    </a>
                </div>
            </div>
        `;
    }

    getUserInfoHTML() {
        const user = this.authManager.getCurrentUser();
        
        // 确保用户对象存在
        if (!user) {
            return '';
        }
        
        // 生成avatar，如果用户没有avatar则创建默认的
        const avatar = user.avatar || {
            type: 'initial',
            color: user.avatar_color || '#4ECDC4',
            text: user.avatar_initial || (user.username ? user.username[0].toUpperCase() : 'U')
        };
        
        const avatarHTML = this.generateAvatarHTML(avatar);

        return `
            ${avatarHTML}
            <span class="user-name" onclick="app.toggleUserMenu()">${user.username || '用户'}</span>
        `;
    }

    getLoginLinksHTML() {
        return `
            <div class="auth-links">
                <a href="login.html" class="login-btn">登录</a>
                <a href="login.html#register" class="register-btn">注册</a>
            </div>
        `;
    }

    generateAvatarHTML(avatar) {
        // 确保avatar对象存在，否则使用默认值
        if (!avatar) {
            avatar = {
                type: 'initial',
                color: '#4ECDC4',
                text: 'U'
            };
        }
        
        if (avatar.type === 'initial') {
            return `
                <div class="user-avatar" onclick="app.toggleUserMenu()">
                    <div class="avatar-initial" style="background-color: ${avatar.color || '#4ECDC4'}">
                        ${avatar.text || 'U'}
                    </div>
                </div>
            `;
        }
        return `
            <div class="user-avatar" onclick="app.toggleUserMenu()">
                <img src="${avatar.url}" alt="用户头像" class="avatar-image">
            </div>
        `;
    }

    getUserTypeLabel(userType) {
        const labels = {
            'patient': '患者',
            'doctor': '医生',
            'researcher': '研究人员',
            'admin': '管理员'
        };
        return labels[userType] || '用户';
    }

    setupUserEventListeners() {
        // 点击页面其他区域关闭用户菜单
        document.addEventListener('click', (e) => {
            const userMenu = document.getElementById('userMenu');
            const userInfo = document.getElementById('userInfo');

            if (userMenu && userInfo && !userInfo.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.classList.remove('show');
            }
        });

        // ESC键关闭菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const userMenu = document.getElementById('userMenu');
                if (userMenu) {
                    userMenu.classList.remove('show');
                }
            }
        });
    }

    toggleUserMenu() {
        const userMenu = document.getElementById('userMenu');
        if (userMenu) {
            userMenu.classList.toggle('show');
        }
    }

    showWelcomeMessage() {
        if (!this.authManager.isAuthenticated()) return;
        
        const user = this.authManager.getCurrentUser();
        const isNewLogin = !sessionStorage.getItem('welcomeShown');
        
        if (isNewLogin) {
            this.showToast(`欢迎回来，${user.username}！`, 'success');
            sessionStorage.setItem('welcomeShown', 'true');
        }
    }

    showUserProfile() {
        // TODO: 实现用户资料页面
        this.showToast('用户资料功能开发中...', 'info');
    }

    showSettings() {
        // TODO: 实现设置页面
        this.showToast('设置功能开发中...', 'info');
    }

    showHelp() {
        // TODO: 实现帮助页面
        this.showToast('帮助功能开发中...', 'info');
    }

    showToast(message, type = 'info') {
        // 创建toast消息
        const toast = document.createElement('div');
        toast.className = `toast-message ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${this.getToastIcon(type)}</span>
                <span class="toast-text">${message}</span>
            </div>
        `;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => toast.classList.add('show'), 100);

        // 自动移除
        setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    getToastIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    showNoDataMessage() {
        document.getElementById('map').innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); color: #666; text-align: center; padding: 40px;">
                <div>
                    <h3 style="color: #667eea; margin-bottom: 16px;">📄 无法加载数据文件</h3>
                    <p style="margin-bottom: 8px;">请确保 1.csv 文件在正确位置</p>
                    <p style="font-size: 14px; color: #999;">文件应包含完整的医院信息</p>
                </div>
            </div>
        `;
    }

    showMapLoadingMessage() {
        document.getElementById('map').innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); color: #666; text-align: center; padding: 40px;">
                <div>
                    <h3 style="color: #667eea; margin-bottom: 16px;">🗺️ 地图加载中...</h3>
                    <p style="margin-bottom: 8px;">正在等待Google Maps API加载</p>
                    <p style="font-size: 14px; color: #999;">如果长时间未加载，请检查网络连接</p>
                </div>
            </div>
        `;
    }

    showErrorMessage(message) {
        document.getElementById('map').innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); color: #666; text-align: center; padding: 40px;">
                <div>
                    <h3 style="color: #667eea; margin-bottom: 16px;">⚠️ 加载失败</h3>
                    <p style="margin-bottom: 8px;">请刷新页面重试</p>
                    <p style="font-size: 14px; color: #999;">${message}</p>
                </div>
            </div>
        `;
    }

    handleURLParams() {
        // 处理分享链接等URL参数
        const params = new URLSearchParams(window.location.search);
        const hash = window.location.hash;
        
        if (hash.includes('#hospital=')) {
            const hospitalId = hash.split('=')[1];
            setTimeout(() => {
                this.mapManager.focusOnHospital(parseInt(hospitalId));
            }, 1000);
        }
    }

    // Google Maps API回调
    onMapsAPIReady() {
        if (this.mapManager && this.dataManager.getAllData().length > 0) {
            // 确保地图容器已准备好
            setTimeout(() => {
                this.mapManager.initMap();
                
                // 额外的地图容器修复
                if (window.fixMapContainer) {
                    setTimeout(() => {
                        window.fixMapContainer();
                    }, 500);
                }
            }, 200);
        }
    }
}

// 全局应用实例
window.app = new MedicalMapApp();

// Google Maps API回调函数
window.initMapCallback = function() {
    if (window.app) {
        window.app.onMapsAPIReady();
    }
};

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', () => {
    window.app.init();
});

// 导出模块
window.MedicalMapApp = MedicalMapApp;