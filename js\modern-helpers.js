// 地图容器修复函数
function fixMapContainer() {
    const mapContainer = document.getElementById('map');
    const mapCard = document.querySelector('.map-card');
    
    if (mapContainer && mapCard) {
        // 强制设置地图容器的尺寸
        const cardRect = mapCard.getBoundingClientRect();
        const headerHeight = document.querySelector('.map-header')?.offsetHeight || 60;
        
        const mapHeight = cardRect.height - headerHeight;
        const mapWidth = cardRect.width;
        
        mapContainer.style.width = mapWidth + 'px';
        mapContainer.style.height = mapHeight + 'px';
        
        // 如果地图已经初始化，触发resize事件
        if (window.app && window.app.mapManager && window.app.mapManager.map) {
            setTimeout(() => {
                google.maps.event.trigger(window.app.mapManager.map, 'resize');
                window.app.mapManager.map.setCenter({ lat: 35.8617, lng: 104.1954 });
            }, 100);
        }
    }
}

// 监听窗口resize事件
function setupMapResize() {
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            fixMapContainer();
        }, 250);
    });
}

// 现代化辅助函数和全局函数
// 这些函数用于支持新的现代化UI

// 切换列表视图
function toggleListView() {
    const hospitalList = document.getElementById('hospitalList');
    const isGridView = hospitalList.classList.contains('grid-view');
    
    if (isGridView) {
        hospitalList.classList.remove('grid-view');
        hospitalList.classList.add('list-view');
    } else {
        hospitalList.classList.remove('list-view');
        hospitalList.classList.add('grid-view');
    }
    
    // 使用现代化UI通知
    if (window.modernUI) {
        window.modernUI.showNotification(
            isGridView ? '已切换到列表视图' : '已切换到网格视图', 
            'info'
        );
    }
}

// 搜索功能增强
function enhanceSearch() {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;
    
    // 添加搜索建议功能
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className = 'search-suggestions md-card';
    suggestionsContainer.style.display = 'none';
    searchInput.parentElement.appendChild(suggestionsContainer);
    
    let searchTimeout;
    searchInput.addEventListener('input', (e) => {
        const query = e.target.value.trim();
        clearTimeout(searchTimeout);
        
        if (query.length > 1) {
            searchTimeout = setTimeout(() => {
                showSearchSuggestions(query, suggestionsContainer);
            }, 300);
        } else {
            suggestionsContainer.style.display = 'none';
        }
    });
    
    // 点击外部隐藏建议
    document.addEventListener('click', (e) => {
        if (!searchInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
            suggestionsContainer.style.display = 'none';
        }
    });
}

// 显示搜索建议
function showSearchSuggestions(query, container) {
    if (!window.app || !window.app.dataManager) return;
    
    const data = window.app.dataManager.hospitals || [];
    const suggestions = [];
    
    // 搜索医院名称
    data.forEach(hospital => {
        if (hospital.name.toLowerCase().includes(query.toLowerCase())) {
            suggestions.push({
                type: 'hospital',
                text: hospital.name,
                subtitle: hospital.province + ' · ' + hospital.city
            });
        }
    });
    
    // 搜索专科
    const specialties = [...new Set(data.map(h => h.specialty))];
    specialties.forEach(specialty => {
        if (specialty.toLowerCase().includes(query.toLowerCase())) {
            suggestions.push({
                type: 'specialty',
                text: specialty,
                subtitle: '专科分类'
            });
        }
    });
    
    // 搜索地区
    const provinces = [...new Set(data.map(h => h.province))];
    provinces.forEach(province => {
        if (province.toLowerCase().includes(query.toLowerCase())) {
            suggestions.push({
                type: 'location',
                text: province,
                subtitle: '省份'
            });
        }
    });
    
    // 限制建议数量
    const limitedSuggestions = suggestions.slice(0, 6);
    
    if (limitedSuggestions.length > 0) {
        container.innerHTML = limitedSuggestions.map(suggestion => `
            <div class=\"suggestion-item\" onclick=\"selectSearchSuggestion('${suggestion.text}', '${suggestion.type}')\">
                <div class=\"suggestion-text body-medium\">${suggestion.text}</div>
                <div class=\"suggestion-subtitle body-small\">${suggestion.subtitle}</div>
            </div>
        `).join('');
        container.style.display = 'block';
    } else {
        container.style.display = 'none';
    }
}

// 选择搜索建议
function selectSearchSuggestion(text, type) {
    const searchInput = document.getElementById('searchInput');
    const suggestionsContainer = document.querySelector('.search-suggestions');
    
    if (searchInput) {
        searchInput.value = text;
        searchInput.dispatchEvent(new Event('input'));
    }
    
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
    
    // 根据类型执行不同的操作
    switch (type) {
        case 'hospital':
            // 直接搜索医院
            break;
        case 'specialty':
            // 按专科筛选
            if (window.app && window.app.dataManager) {
                window.app.dataManager.filterByCategory(text);
                window.app.uiManager.updateUI();
            }
            break;
        case 'location':
            // 按地区筛选
            if (window.app && window.app.dataManager) {
                window.app.dataManager.filterByLocation(text);
                window.app.uiManager.updateUI();
            }
            break;
    }
}

// 地图全屏切换
function toggleMapFullscreen() {
    const mapSection = document.querySelector('.map-section');
    const mapContainer = document.querySelector('.map-container');
    
    if (!mapSection || !mapContainer) return;
    
    if (mapSection.classList.contains('fullscreen-mode')) {
        // 退出全屏
        mapSection.classList.remove('fullscreen-mode');
        mapContainer.style.position = 'relative';
        mapContainer.style.zIndex = 'auto';
        
        if (window.modernUI) {
            window.modernUI.showNotification('已退出全屏模式', 'info');
        }
    } else {
        // 进入全屏
        mapSection.classList.add('fullscreen-mode');
        mapContainer.style.position = 'fixed';
        mapContainer.style.top = '0';
        mapContainer.style.left = '0';
        mapContainer.style.width = '100vw';
        mapContainer.style.height = '100vh';
        mapContainer.style.zIndex = '9999';
        mapContainer.style.background = 'var(--md-sys-color-surface)';
        
        if (window.modernUI) {
            window.modernUI.showNotification('已进入全屏模式，按ESC或点击退出按钮退出', 'info');
        }
    }
    
    // 触发地图重绘
    if (window.app && window.app.mapManager && window.app.mapManager.map) {
        setTimeout(() => {
            google.maps.event.trigger(window.app.mapManager.map, 'resize');
        }, 300);
    }
}

// 键盘快捷键支持
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // ESC退出全屏
        if (e.key === 'Escape') {
            const mapSection = document.querySelector('.map-section');
            if (mapSection && mapSection.classList.contains('fullscreen-mode')) {
                toggleMapFullscreen();
            }
        }
        
        // Ctrl/Cmd + K 聚焦搜索
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Ctrl/Cmd + D 切换深色模式
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            if (window.modernUI) {
                window.modernUI.toggleTheme();
            }
        }
    });
}

// 页面可见性API - 优化性能
function setupVisibilityOptimization() {
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            // 页面隐藏时暂停动画和更新
            if (window.app && window.app.chatManager) {
                window.app.chatManager.stopRefreshIntervals();
            }
        } else {
            // 页面显示时恢复
            if (window.app && window.app.chatManager && window.app.chatManager.isOpen) {
                window.app.chatManager.startRefreshIntervals();
            }
        }
    });
}

// 初始化现代化功能
function initModernFeatures() {
    // 确保在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initModernFeaturesCore, 100);
        });
    } else {
        setTimeout(initModernFeaturesCore, 100);
    }
}

function initModernFeaturesCore() {
    enhanceSearch();
    setupKeyboardShortcuts();
    setupVisibilityOptimization();
    setupMapResize(); // 添加地图resize监听
    
    // 在DOM加载完成后修复地图容器
    setTimeout(() => {
        fixMapContainer();
    }, 1000);
    
    // 添加搜索建议样式
    if (!document.querySelector('style[data-search-suggestions]')) {
        const style = document.createElement('style');
        style.setAttribute('data-search-suggestions', 'true');
        style.textContent = `
            .search-suggestions {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--md-sys-color-surface-container);
                border: 1px solid var(--md-sys-color-outline-variant);
                border-radius: var(--md-sys-shape-corner-medium);
                box-shadow: var(--md-sys-elevation-level2);
                z-index: 1000;
                max-height: 300px;
                overflow-y: auto;
                margin-top: 4px;
            }
            
            .suggestion-item {
                padding: var(--md-sys-spacing-lg);
                cursor: pointer;
                border-bottom: 1px solid var(--md-sys-color-outline-variant);
                transition: background-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
            }
            
            .suggestion-item:last-child {
                border-bottom: none;
            }
            
            .suggestion-item:hover {
                background-color: var(--md-sys-color-secondary-container);
            }
            
            .suggestion-text {
                color: var(--md-sys-color-on-surface);
                font-weight: 500;
            }
            
            .suggestion-subtitle {
                color: var(--md-sys-color-on-surface-variant);
                margin-top: var(--md-sys-spacing-xs);
            }
            
            .hospital-list.grid-view {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: var(--md-sys-spacing-lg);
            }
            
            .hospital-list.list-view {
                display: flex;
                flex-direction: column;
                gap: var(--md-sys-spacing-md);
            }
            
            .fullscreen-mode {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                z-index: 9999 !important;
                background: var(--md-sys-color-surface) !important;
            }
            
            .fullscreen-mode .map-card {
                border-radius: 0 !important;
                height: 100vh !important;
            }
            
            .fullscreen-mode .map-container {
                border-radius: 0 !important;
            }
        `;
        document.head.appendChild(style);
    }
}

// 平滑滚动到指定区域
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 全局函数，供HTML调用
window.toggleListView = toggleListView;
window.fixMapContainer = fixMapContainer;
window.scrollToSection = scrollToSection;
window.toggleMapFullscreen = toggleMapFullscreen;

// 启动现代化功能
initModernFeatures();