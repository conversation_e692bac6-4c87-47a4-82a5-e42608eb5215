// 现代化UI增强脚本
class ModernUIManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }
    
    init() {
        this.setupTheme();
        this.setupThemeToggle();
        this.setupAnimations();
        this.setupInteractions();
        this.setupModernComponents();
    }
    
    // 主题管理
    setupTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        this.updateThemeButton();
    }
    
    setupThemeToggle() {
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }
    
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);
        this.updateThemeButton();
        
        // 添加切换动画
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }
    
    updateThemeButton() {
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('span');
            if (icon) {
                icon.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
            }
            themeToggle.title = this.currentTheme === 'light' ? '切换深色模式' : '切换浅色模式';
        }
    }
    
    // 动画设置
    setupAnimations() {
        // 页面加载动画
        this.animateOnLoad();
        
        // 滚动动画
        this.setupScrollAnimations();
        
        // 悬停动画
        this.setupHoverAnimations();
    }
    
    animateOnLoad() {
        // Hero区域动画
        const heroTitle = document.querySelector('.hero-title');
        const heroSubtitle = document.querySelector('.hero-subtitle');
        const heroFeatures = document.querySelector('.hero-features');
        const searchCard = document.querySelector('.search-card');
        
        if (heroTitle) {
            heroTitle.style.opacity = '0';
            heroTitle.style.transform = 'translateY(30px)';
            setTimeout(() => {
                heroTitle.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                heroTitle.style.opacity = '1';
                heroTitle.style.transform = 'translateY(0)';
            }, 100);
        }
        
        if (heroSubtitle) {
            heroSubtitle.style.opacity = '0';
            heroSubtitle.style.transform = 'translateY(30px)';
            setTimeout(() => {
                heroSubtitle.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                heroSubtitle.style.opacity = '1';
                heroSubtitle.style.transform = 'translateY(0)';
            }, 200);
        }
        
        if (heroFeatures) {
            heroFeatures.style.opacity = '0';
            heroFeatures.style.transform = 'translateY(30px)';
            setTimeout(() => {
                heroFeatures.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                heroFeatures.style.opacity = '1';
                heroFeatures.style.transform = 'translateY(0)';
            }, 300);
        }
        
        if (searchCard) {
            searchCard.style.opacity = '0';
            searchCard.style.transform = 'translateX(30px)';
            setTimeout(() => {
                searchCard.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                searchCard.style.opacity = '1';
                searchCard.style.transform = 'translateX(0)';
            }, 400);
        }
        
        // 统计卡片动画
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            setTimeout(() => {
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 500 + index * 100);
        });
    }
    
    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        // 观察需要动画的元素
        const animateElements = document.querySelectorAll('.md-card, .hospital-item');
        animateElements.forEach(el => {
            observer.observe(el);
        });
    }
    
    setupHoverAnimations() {
        // 卡片悬停效果
        const cards = document.querySelectorAll('.md-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
                card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
        
        // 按钮涟漪效果
        this.setupRippleEffect();
    }
    
    setupRippleEffect() {
        const buttons = document.querySelectorAll('.md-button, .md-fab, .filter-button');
        
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.className = 'ripple';
                
                button.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
        
        // 添加涟漪效果样式
        if (!document.querySelector('style[data-ripple]')) {
            const style = document.createElement('style');
            style.setAttribute('data-ripple', 'true');
            style.textContent = `
                .ripple {
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    transform: scale(0);
                    animation: ripple-animation 0.6s linear;
                    pointer-events: none;
                }
                
                @keyframes ripple-animation {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
                
                .animate-in {
                    animation: slide-in-up 0.6s ease;
                }
                
                @keyframes slide-in-up {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }\n    }\n    \n    setupInteractions() {\n        // 地图控制按钮\n        this.setupMapControls();\n        \n        // 搜索增强\n        this.setupSearchEnhancements();\n        \n        // 筛选按钮交互\n        this.setupFilterInteractions();\n    }\n    \n    setupMapControls() {\n        const locateBtn = document.getElementById('locateBtn');\n        const fullscreenBtn = document.getElementById('fullscreenBtn');\n        \n        if (locateBtn) {\n            locateBtn.addEventListener('click', () => {\n                this.locateUser();\n            });\n        }\n        \n        if (fullscreenBtn) {\n            fullscreenBtn.addEventListener('click', () => {\n                this.toggleMapFullscreen();\n            });\n        }\n    }\n    \n    locateUser() {\n        if (navigator.geolocation) {\n            const btn = document.getElementById('locateBtn');\n            btn.style.transform = 'scale(0.9)';\n            btn.style.transition = 'transform 0.1s ease';\n            \n            setTimeout(() => {\n                btn.style.transform = 'scale(1)';\n            }, 100);\n            \n            navigator.geolocation.getCurrentPosition(\n                (position) => {\n                    // 这里可以集成到地图管理器\n                    console.log('User location:', position.coords);\n                    this.showNotification('已定位到您的位置', 'success');\n                },\n                (error) => {\n                    this.showNotification('定位失败，请检查位置权限', 'error');\n                }\n            );\n        } else {\n            this.showNotification('浏览器不支持定位功能', 'error');\n        }\n    }\n    \n    toggleMapFullscreen() {\n        const mapSection = document.querySelector('.map-section');\n        const btn = document.getElementById('fullscreenBtn');\n        \n        if (mapSection.classList.contains('fullscreen')) {\n            mapSection.classList.remove('fullscreen');\n            btn.querySelector('span').textContent = '⛶';\n            btn.title = '全屏显示';\n        } else {\n            mapSection.classList.add('fullscreen');\n            btn.querySelector('span').textContent = '⛶';\n            btn.title = '退出全屏';\n        }\n    }\n    \n    setupSearchEnhancements() {\n        const searchInput = document.getElementById('searchInput');\n        const searchBtn = document.querySelector('.search-btn');\n        \n        if (searchInput) {\n            // 搜索输入增强\n            searchInput.addEventListener('focus', () => {\n                searchInput.parentElement.style.transform = 'scale(1.02)';\n                searchInput.parentElement.style.transition = 'transform 0.2s ease';\n            });\n            \n            searchInput.addEventListener('blur', () => {\n                searchInput.parentElement.style.transform = 'scale(1)';\n            });\n            \n            // 实时搜索提示\n            let searchTimeout;\n            searchInput.addEventListener('input', () => {\n                clearTimeout(searchTimeout);\n                searchTimeout = setTimeout(() => {\n                    this.handleSearchInput(searchInput.value);\n                }, 300);\n            });\n        }\n        \n        if (searchBtn) {\n            searchBtn.addEventListener('click', () => {\n                this.performSearch();\n            });\n        }\n    }\n    \n    handleSearchInput(query) {\n        if (query.length > 2) {\n            // 这里可以添加搜索建议功能\n            console.log('搜索查询:', query);\n        }\n    }\n    \n    performSearch() {\n        const searchInput = document.getElementById('searchInput');\n        const query = searchInput?.value.trim();\n        \n        if (query) {\n            this.showNotification(`正在搜索: ${query}`, 'info');\n        }\n    }\n    \n    setupFilterInteractions() {\n        // 将在筛选按钮创建后设置\n        document.addEventListener('click', (e) => {\n            if (e.target.classList.contains('filter-button')) {\n                this.handleFilterClick(e.target);\n            }\n        });\n    }\n    \n    handleFilterClick(button) {\n        const filterButtons = document.querySelectorAll('.filter-button');\n        filterButtons.forEach(btn => btn.classList.remove('active'));\n        button.classList.add('active');\n        \n        // 添加点击反馈\n        button.style.transform = 'scale(0.95)';\n        setTimeout(() => {\n            button.style.transform = 'scale(1)';\n        }, 100);\n    }\n    \n    setupModernComponents() {\n        // 添加现代化组件行为\n        this.setupTooltips();\n        this.setupProgressBars();\n    }\n    \n    setupTooltips() {\n        const elementsWithTooltips = document.querySelectorAll('[title]');\n        elementsWithTooltips.forEach(element => {\n            element.addEventListener('mouseenter', (e) => {\n                this.showTooltip(e.target, e.target.title);\n            });\n            \n            element.addEventListener('mouseleave', () => {\n                this.hideTooltip();\n            });\n        });\n    }\n    \n    showTooltip(element, text) {\n        const tooltip = document.createElement('div');\n        tooltip.className = 'modern-tooltip';\n        tooltip.textContent = text;\n        \n        const rect = element.getBoundingClientRect();\n        tooltip.style.left = rect.left + rect.width / 2 + 'px';\n        tooltip.style.top = rect.top - 35 + 'px';\n        \n        document.body.appendChild(tooltip);\n        \n        setTimeout(() => {\n            tooltip.style.opacity = '1';\n            tooltip.style.transform = 'translateY(0)';\n        }, 10);\n    }\n    \n    hideTooltip() {\n        const tooltip = document.querySelector('.modern-tooltip');\n        if (tooltip) {\n            tooltip.remove();\n        }\n    }\n    \n    setupProgressBars() {\n        // 页面加载进度\n        this.showLoadingProgress();\n    }\n    \n    showLoadingProgress() {\n        const progressBar = document.createElement('div');\n        progressBar.className = 'loading-progress';\n        progressBar.innerHTML = '<div class=\"progress-fill\"></div>';\n        \n        document.body.appendChild(progressBar);\n        \n        let progress = 0;\n        const interval = setInterval(() => {\n            progress += Math.random() * 30;\n            if (progress >= 100) {\n                progress = 100;\n                clearInterval(interval);\n                setTimeout(() => {\n                    progressBar.remove();\n                }, 300);\n            }\n            \n            const fill = progressBar.querySelector('.progress-fill');\n            if (fill) {\n                fill.style.width = progress + '%';\n            }\n        }, 200);\n    }\n    \n    // 通知系统\n    showNotification(message, type = 'info') {\n        const notification = document.createElement('div');\n        notification.className = `modern-notification ${type}`;\n        notification.textContent = message;\n        \n        const icons = {\n            success: '✅',\n            error: '❌',\n            info: 'ℹ️',\n            warning: '⚠️'\n        };\n        \n        notification.innerHTML = `\n            <span class=\"notification-icon\">${icons[type] || icons.info}</span>\n            <span class=\"notification-text\">${message}</span>\n        `;\n        \n        document.body.appendChild(notification);\n        \n        setTimeout(() => {\n            notification.classList.add('show');\n        }, 10);\n        \n        setTimeout(() => {\n            notification.classList.remove('show');\n            setTimeout(() => {\n                notification.remove();\n            }, 300);\n        }, 3000);\n    }\n    \n    // 公共方法\n    updateStats(stats) {\n        const statElements = {\n            totalHospitals: document.getElementById('totalHospitals'),\n            totalSpecialties: document.getElementById('totalSpecialties'),\n            totalProvinces: document.getElementById('totalProvinces'),\n            totalCities: document.getElementById('totalCities')\n        };\n        \n        Object.keys(statElements).forEach(key => {\n            const element = statElements[key];\n            if (element && stats[key] !== undefined) {\n                this.animateNumber(element, 0, stats[key], 2000);\n            }\n        });\n    }\n    \n    animateNumber(element, start, end, duration) {\n        const startTime = Date.now();\n        const difference = end - start;\n        \n        const step = () => {\n            const progress = Math.min((Date.now() - startTime) / duration, 1);\n            const currentValue = Math.floor(start + difference * this.easeOutCubic(progress));\n            element.textContent = currentValue.toLocaleString();\n            \n            if (progress < 1) {\n                requestAnimationFrame(step);\n            }\n        };\n        \n        requestAnimationFrame(step);\n    }\n    \n    easeOutCubic(t) {\n        return 1 - Math.pow(1 - t, 3);\n    }\n}\n\n// 添加现代化样式\nif (!document.querySelector('style[data-modern-ui]')) {\n    const style = document.createElement('style');\n    style.setAttribute('data-modern-ui', 'true');\n    style.textContent = `\n        .fullscreen {\n            position: fixed !important;\n            top: 0 !important;\n            left: 0 !important;\n            width: 100vw !important;\n            height: 100vh !important;\n            z-index: 9999 !important;\n            background: var(--md-sys-color-surface) !important;\n        }\n        \n        .modern-tooltip {\n            position: fixed;\n            background: var(--md-sys-color-surface-container-highest);\n            color: var(--md-sys-color-on-surface);\n            padding: 8px 12px;\n            border-radius: 6px;\n            font-size: 12px;\n            opacity: 0;\n            transform: translateY(-5px);\n            transition: opacity 0.2s ease, transform 0.2s ease;\n            pointer-events: none;\n            z-index: 10000;\n            box-shadow: var(--md-sys-elevation-level2);\n        }\n        \n        .loading-progress {\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100%;\n            height: 3px;\n            background: var(--md-sys-color-surface-variant);\n            z-index: 10001;\n        }\n        \n        .progress-fill {\n            height: 100%;\n            background: var(--md-sys-color-primary);\n            width: 0%;\n            transition: width 0.3s ease;\n        }\n        \n        .modern-notification {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            background: var(--md-sys-color-surface-container-high);\n            color: var(--md-sys-color-on-surface);\n            padding: 16px 20px;\n            border-radius: 12px;\n            box-shadow: var(--md-sys-elevation-level3);\n            transform: translateX(400px);\n            transition: transform 0.3s ease;\n            z-index: 10002;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            max-width: 400px;\n        }\n        \n        .modern-notification.show {\n            transform: translateX(0);\n        }\n        \n        .modern-notification.success {\n            border-left: 4px solid var(--md-sys-color-primary);\n        }\n        \n        .modern-notification.error {\n            border-left: 4px solid var(--md-sys-color-error);\n        }\n        \n        .modern-notification.warning {\n            border-left: 4px solid #FF9800;\n        }\n        \n        .modern-notification.info {\n            border-left: 4px solid var(--md-sys-color-tertiary);\n        }\n        \n        .notification-icon {\n            font-size: 20px;\n        }\n        \n        .notification-text {\n            flex: 1;\n            font-weight: 500;\n        }\n    `;\n    document.head.appendChild(style);\n}\n\n// 全局实例\nwindow.modernUI = null;\n\n// 页面加载完成后初始化\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', () => {\n        window.modernUI = new ModernUIManager();\n    });\n} else {\n    window.modernUI = new ModernUIManager();\n}