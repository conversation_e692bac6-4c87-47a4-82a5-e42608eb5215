<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 地域医疗专长地图</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/header.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/user-status.css">
    <link rel="stylesheet" href="css/profile.css">
</head>
<body>
    <div class="bg-decoration"></div>
    
    <!-- 项目头部 -->
    <div class="gov-header">
        <div class="gov-header-content">
            <div class="gov-logo">
                <div class="gov-emblem"></div>
                <span>医疗专长地图</span>
            </div>
            <div class="gov-links">
                <a href="index.html">返回首页</a>
                <!-- 用户状态将通过JavaScript动态添加到这里 -->
                <div class="user-status" id="userStatus"></div>
            </div>
        </div>
    </div>

    <div class="profile-container">
        <div class="profile-header">
            <div class="profile-avatar" id="profileAvatar">
                <!-- 头像将通过JS动态生成 -->
            </div>
            <div class="profile-info">
                <h1 id="profileName">加载中...</h1>
                <p id="profileType">用户类型</p>
                <p class="profile-meta">
                    <span id="joinDate">加入时间：--</span>
                    <span id="loginCount">登录次数：--</span>
                </p>
            </div>
            <div class="profile-actions">
                <button class="btn-primary" onclick="editProfile()">编辑资料</button>
                <button class="btn-secondary" onclick="changePassword()">修改密码</button>
            </div>
        </div>

        <div class="profile-content">
            <!-- 左侧导航 -->
            <div class="profile-sidebar">
                <div class="sidebar-menu">
                    <a href="#basic" class="menu-item active" data-tab="basic">
                        <span class="menu-icon">👤</span>
                        基本信息
                    </a>
                    <a href="#activity" class="menu-item" data-tab="activity">
                        <span class="menu-icon">📊</span>
                        活动统计
                    </a>
                    <a href="#favorites" class="menu-item" data-tab="favorites">
                        <span class="menu-icon">❤️</span>
                        收藏医院
                    </a>
                    <a href="#messages" class="menu-item" data-tab="messages">
                        <span class="menu-icon">💬</span>
                        我的消息
                    </a>
                    <a href="#settings" class="menu-item" data-tab="settings">
                        <span class="menu-icon">⚙️</span>
                        设置
                    </a>
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="profile-main">
                <!-- 基本信息 -->
                <div class="tab-content active" id="tab-basic">
                    <div class="content-header">
                        <h2>基本信息</h2>
                        <button class="btn-text" onclick="editProfile()">编辑</button>
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <label>用户名</label>
                            <span id="displayUsername">--</span>
                        </div>
                        <div class="info-item">
                            <label>邮箱</label>
                            <span id="displayEmail">--</span>
                        </div>
                        <div class="info-item">
                            <label>用户类型</label>
                            <span id="displayUserType">--</span>
                        </div>
                        <div class="info-item">
                            <label>个人简介</label>
                            <span id="displayBio">暂无简介</span>
                        </div>
                        <div class="info-item">
                            <label>所在地区</label>
                            <span id="displayLocation">未设置</span>
                        </div>
                        <div class="info-item">
                            <label>专业领域</label>
                            <span id="displaySpecialties">未设置</span>
                        </div>
                    </div>
                </div>

                <!-- 活动统计 -->
                <div class="tab-content" id="tab-activity">
                    <div class="content-header">
                        <h2>活动统计</h2>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">🏥</div>
                            <div class="stat-content">
                                <span class="stat-number" id="statFavorites">0</span>
                                <span class="stat-label">收藏医院</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">❓</div>
                            <div class="stat-content">
                                <span class="stat-number" id="statQuestions">0</span>
                                <span class="stat-label">提问次数</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">💬</div>
                            <div class="stat-content">
                                <span class="stat-number" id="statAnswers">0</span>
                                <span class="stat-label">回答次数</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">👍</div>
                            <div class="stat-content">
                                <span class="stat-number" id="statHelpful">0</span>
                                <span class="stat-label">获得点赞</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收藏医院 -->
                <div class="tab-content" id="tab-favorites">
                    <div class="content-header">
                        <h2>收藏的医院</h2>
                    </div>
                    
                    <div class="favorites-list" id="favoritesList">
                        <div class="empty-state">
                            <div class="empty-icon">🏥</div>
                            <p>还没有收藏任何医院</p>
                            <a href="index.html" class="btn-primary">去收藏医院</a>
                        </div>
                    </div>
                </div>

                <!-- 我的消息 -->
                <div class="tab-content" id="tab-messages">
                    <div class="content-header">
                        <h2>我的消息</h2>
                        <button class="btn-primary" onclick="composeMessage()">发送消息</button>
                    </div>
                    
                    <div class="messages-container">
                        <div class="message-filters">
                            <button class="filter-btn active" data-filter="all">全部</button>
                            <button class="filter-btn" data-filter="unread">未读</button>
                            <button class="filter-btn" data-filter="sent">已发送</button>
                        </div>
                        
                        <div class="messages-list" id="messagesList">
                            <div class="empty-state">
                                <div class="empty-icon">💬</div>
                                <p>暂无消息</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设置 -->
                <div class="tab-content" id="tab-settings">
                    <div class="content-header">
                        <h2>账户设置</h2>
                    </div>
                    
                    <div class="settings-sections">
                        <div class="settings-section">
                            <h3>隐私设置</h3>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>个人资料公开性</label>
                                    <p>设置其他用户是否可以查看您的个人资料</p>
                                </div>
                                <select id="privacySetting">
                                    <option value="public">公开</option>
                                    <option value="private">私密</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h3>通知设置</h3>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>接收通知</label>
                                    <p>是否接收系统通知和消息提醒</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" id="notificationSetting" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h3>账户管理</h3>
                            <div class="setting-item">
                                <button class="btn-secondary" onclick="exportData()">导出我的数据</button>
                                <button class="btn-danger" onclick="deleteAccount()">删除账户</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑资料模态框 -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑个人资料</h3>
                <button class="modal-close" onclick="closeModal('editProfileModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editProfileForm">
                    <div class="form-group">
                        <label for="editUsername">用户名</label>
                        <input type="text" id="editUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editBio">个人简介</label>
                        <textarea id="editBio" name="bio" rows="3" placeholder="介绍一下自己..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editLocation">所在地区</label>
                        <input type="text" id="editLocation" name="location" placeholder="如：北京市">
                    </div>
                    <div class="form-group">
                        <label for="editSpecialties">专业领域</label>
                        <input type="text" id="editSpecialties" name="specialties" placeholder="如：心脏病、糖尿病（用逗号分隔）">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeModal('editProfileModal')">取消</button>
                <button class="btn-primary" onclick="saveProfile()">保存</button>
            </div>
        </div>
    </div>

    <!-- 修改密码模态框 -->
    <div class="modal" id="changePasswordModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>修改密码</h3>
                <button class="modal-close" onclick="closeModal('changePasswordModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="form-group">
                        <label for="currentPassword">当前密码</label>
                        <input type="password" id="currentPassword" name="currentPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">新密码</label>
                        <input type="password" id="newPassword" name="newPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="confirmNewPassword">确认新密码</label>
                        <input type="password" id="confirmNewPassword" name="confirmNewPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeModal('changePasswordModal')">取消</button>
                <button class="btn-primary" onclick="savePassword()">保存</button>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/auth-manager.js"></script>
    <script src="js/message-manager.js"></script>
    <script src="js/profile.js"></script>
</body>
</html>