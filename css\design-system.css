/* Material Design 3 设计系统 - 优化配色版本 */
:root {
  /* 优化的色彩系统 - 更温和舒适的医疗主题配色 */
  --md-sys-color-primary: #0277BD;
  --md-sys-color-on-primary: #FFFFFF;
  --md-sys-color-primary-container: #B3E5FC;
  --md-sys-color-on-primary-container: #01579B;
  
  --md-sys-color-secondary: #26A69A;
  --md-sys-color-on-secondary: #FFFFFF;
  --md-sys-color-secondary-container: #B2DFDB;
  --md-sys-color-on-secondary-container: #004D40;
  
  --md-sys-color-tertiary: #7B1FA2;
  --md-sys-color-on-tertiary: #FFFFFF;
  --md-sys-color-tertiary-container: #E1BEE7;
  --md-sys-color-on-tertiary-container: #4A148C;
  
  --md-sys-color-error: #D32F2F;
  --md-sys-color-on-error: #FFFFFF;
  --md-sys-color-error-container: #FFCDD2;
  --md-sys-color-on-error-container: #B71C1C;
  
  --md-sys-color-surface: #FFFFFF;
  --md-sys-color-on-surface: #1A1A1A;
  --md-sys-color-surface-variant: #F5F5F5;
  --md-sys-color-on-surface-variant: #424242;
  
  --md-sys-color-outline: #BDBDBD;
  --md-sys-color-outline-variant: #E0E0E0;
  
  --md-sys-color-background: #FAFAFA;
  --md-sys-color-on-background: #1A1A1A;
  
  --md-sys-color-surface-container-lowest: #FFFFFF;
  --md-sys-color-surface-container-low: #FCFCFC;
  --md-sys-color-surface-container: #F6F6F6;
  --md-sys-color-surface-container-high: #F0F0F0;
  --md-sys-color-surface-container-highest: #EBEBEB;
  
  /* 深色模式色彩 - 优化后 */
  --md-sys-color-primary-dark: #81D4FA;
  --md-sys-color-on-primary-dark: #01579B;
  --md-sys-color-primary-container-dark: #0277BD;
  --md-sys-color-on-primary-container-dark: #E1F5FE;
  
  --md-sys-color-surface-dark: #121212;
  --md-sys-color-on-surface-dark: #E0E0E0;
  --md-sys-color-background-dark: #121212;
  --md-sys-color-on-background-dark: #E0E0E0;
  --md-sys-color-surface-container-dark: #1E1E1E;
  --md-sys-color-surface-variant-dark: #2A2A2A;
  --md-sys-color-on-surface-variant-dark: #B0B0B0;
  
  /* 字体系统 */
  --md-sys-typescale-display-large-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-display-large-size: 57px;
  --md-sys-typescale-display-large-weight: 400;
  --md-sys-typescale-display-large-line-height: 64px;
  
  --md-sys-typescale-display-medium-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-display-medium-size: 45px;
  --md-sys-typescale-display-medium-weight: 400;
  --md-sys-typescale-display-medium-line-height: 52px;
  
  --md-sys-typescale-display-small-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-display-small-size: 36px;
  --md-sys-typescale-display-small-weight: 400;
  --md-sys-typescale-display-small-line-height: 44px;
  
  --md-sys-typescale-headline-large-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-headline-large-size: 32px;
  --md-sys-typescale-headline-large-weight: 400;
  --md-sys-typescale-headline-large-line-height: 40px;
  
  --md-sys-typescale-headline-medium-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-headline-medium-size: 28px;
  --md-sys-typescale-headline-medium-weight: 400;
  --md-sys-typescale-headline-medium-line-height: 36px;
  
  --md-sys-typescale-headline-small-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-headline-small-size: 24px;
  --md-sys-typescale-headline-small-weight: 400;
  --md-sys-typescale-headline-small-line-height: 32px;
  
  --md-sys-typescale-title-large-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-title-large-size: 22px;
  --md-sys-typescale-title-large-weight: 400;
  --md-sys-typescale-title-large-line-height: 28px;
  
  --md-sys-typescale-title-medium-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-title-medium-size: 16px;
  --md-sys-typescale-title-medium-weight: 500;
  --md-sys-typescale-title-medium-line-height: 24px;
  
  --md-sys-typescale-title-small-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-title-small-size: 14px;
  --md-sys-typescale-title-small-weight: 500;
  --md-sys-typescale-title-small-line-height: 20px;
  
  --md-sys-typescale-body-large-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-body-large-size: 16px;
  --md-sys-typescale-body-large-weight: 400;
  --md-sys-typescale-body-large-line-height: 24px;
  
  --md-sys-typescale-body-medium-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-body-medium-size: 14px;
  --md-sys-typescale-body-medium-weight: 400;
  --md-sys-typescale-body-medium-line-height: 20px;
  
  --md-sys-typescale-body-small-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-body-small-size: 12px;
  --md-sys-typescale-body-small-weight: 400;
  --md-sys-typescale-body-small-line-height: 16px;
  
  --md-sys-typescale-label-large-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-label-large-size: 14px;
  --md-sys-typescale-label-large-weight: 500;
  --md-sys-typescale-label-large-line-height: 20px;
  
  --md-sys-typescale-label-medium-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-label-medium-size: 12px;
  --md-sys-typescale-label-medium-weight: 500;
  --md-sys-typescale-label-medium-line-height: 16px;
  
  --md-sys-typescale-label-small-font: 'Google Sans', system-ui, -apple-system, sans-serif;
  --md-sys-typescale-label-small-size: 11px;
  --md-sys-typescale-label-small-weight: 500;
  --md-sys-typescale-label-small-line-height: 16px;
  
  /* 阴影系统 */
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);
  
  /* 形状系统 */
  --md-sys-shape-corner-none: 0px;
  --md-sys-shape-corner-extra-small: 4px;
  --md-sys-shape-corner-small: 8px;
  --md-sys-shape-corner-medium: 12px;
  --md-sys-shape-corner-large: 16px;
  --md-sys-shape-corner-extra-large: 28px;
  --md-sys-shape-corner-full: 1000px;
  
  /* 动画系统 */
  --md-sys-motion-duration-short1: 50ms;
  --md-sys-motion-duration-short2: 100ms;
  --md-sys-motion-duration-short3: 150ms;
  --md-sys-motion-duration-short4: 200ms;
  --md-sys-motion-duration-medium1: 250ms;
  --md-sys-motion-duration-medium2: 300ms;
  --md-sys-motion-duration-medium3: 350ms;
  --md-sys-motion-duration-medium4: 400ms;
  --md-sys-motion-duration-long1: 450ms;
  --md-sys-motion-duration-long2: 500ms;
  --md-sys-motion-duration-long3: 550ms;
  --md-sys-motion-duration-long4: 600ms;
  --md-sys-motion-duration-extra-long1: 700ms;
  --md-sys-motion-duration-extra-long2: 800ms;
  --md-sys-motion-duration-extra-long3: 900ms;
  --md-sys-motion-duration-extra-long4: 1000ms;
  
  --md-sys-motion-easing-linear: cubic-bezier(0, 0, 1, 1);
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-standard-accelerate: cubic-bezier(0.3, 0, 1, 1);
  --md-sys-motion-easing-standard-decelerate: cubic-bezier(0, 0, 0, 1);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized-accelerate: cubic-bezier(0.3, 0, 0.8, 0.15);
  --md-sys-motion-easing-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1);
  
  /* 间距系统 */
  --md-sys-spacing-xs: 4px;
  --md-sys-spacing-sm: 8px;
  --md-sys-spacing-md: 12px;
  --md-sys-spacing-lg: 16px;
  --md-sys-spacing-xl: 20px;
  --md-sys-spacing-2xl: 24px;
  --md-sys-spacing-3xl: 32px;
  --md-sys-spacing-4xl: 40px;
  --md-sys-spacing-5xl: 48px;
  --md-sys-spacing-6xl: 64px;
}

/* 深色模式 */
[data-theme="dark"] {
  --md-sys-color-primary: var(--md-sys-color-primary-dark);
  --md-sys-color-on-primary: var(--md-sys-color-on-primary-dark);
  --md-sys-color-primary-container: var(--md-sys-color-primary-container-dark);
  --md-sys-color-on-primary-container: var(--md-sys-color-on-primary-container-dark);
  
  --md-sys-color-surface: var(--md-sys-color-surface-dark);
  --md-sys-color-on-surface: var(--md-sys-color-on-surface-dark);
  --md-sys-color-background: var(--md-sys-color-background-dark);
  --md-sys-color-on-background: var(--md-sys-color-on-background-dark);
  
  --md-sys-color-surface-container-lowest: #0A0A0A;
  --md-sys-color-surface-container-low: var(--md-sys-color-surface-container-dark);
  --md-sys-color-surface-container: var(--md-sys-color-surface-variant-dark);
  --md-sys-color-surface-container-high: #333333;
  --md-sys-color-surface-container-highest: #3D3D3D;
  
  --md-sys-color-surface-variant: var(--md-sys-color-surface-variant-dark);
  --md-sys-color-on-surface-variant: var(--md-sys-color-on-surface-variant-dark);
  --md-sys-color-outline: #666666;
  --md-sys-color-outline-variant: #404040;
}

/* 字体类 */
.display-large {
  font-family: var(--md-sys-typescale-display-large-font);
  font-size: var(--md-sys-typescale-display-large-size);
  font-weight: var(--md-sys-typescale-display-large-weight);
  line-height: var(--md-sys-typescale-display-large-line-height);
}

.display-medium {
  font-family: var(--md-sys-typescale-display-medium-font);
  font-size: var(--md-sys-typescale-display-medium-size);
  font-weight: var(--md-sys-typescale-display-medium-weight);
  line-height: var(--md-sys-typescale-display-medium-line-height);
}

.display-small {
  font-family: var(--md-sys-typescale-display-small-font);
  font-size: var(--md-sys-typescale-display-small-size);
  font-weight: var(--md-sys-typescale-display-small-weight);
  line-height: var(--md-sys-typescale-display-small-line-height);
}

.headline-large {
  font-family: var(--md-sys-typescale-headline-large-font);
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  line-height: var(--md-sys-typescale-headline-large-line-height);
}

.headline-medium {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  line-height: var(--md-sys-typescale-headline-medium-line-height);
}

.headline-small {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
}

.title-large {
  font-family: var(--md-sys-typescale-title-large-font);
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-title-large-weight);
  line-height: var(--md-sys-typescale-title-large-line-height);
}

.title-medium {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: var(--md-sys-typescale-title-medium-weight);
  line-height: var(--md-sys-typescale-title-medium-line-height);
}

.title-small {
  font-family: var(--md-sys-typescale-title-small-font);
  font-size: var(--md-sys-typescale-title-small-size);
  font-weight: var(--md-sys-typescale-title-small-weight);
  line-height: var(--md-sys-typescale-title-small-line-height);
}

.body-large {
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
}

.body-medium {
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: var(--md-sys-typescale-body-medium-weight);
  line-height: var(--md-sys-typescale-body-medium-line-height);
}

.body-small {
  font-family: var(--md-sys-typescale-body-small-font);
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: var(--md-sys-typescale-body-small-weight);
  line-height: var(--md-sys-typescale-body-small-line-height);
}

.label-large {
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
}

.label-medium {
  font-family: var(--md-sys-typescale-label-medium-font);
  font-size: var(--md-sys-typescale-label-medium-size);
  font-weight: var(--md-sys-typescale-label-medium-weight);
  line-height: var(--md-sys-typescale-label-medium-line-height);
}

.label-small {
  font-family: var(--md-sys-typescale-label-small-font);
  font-size: var(--md-sys-typescale-label-small-size);
  font-weight: var(--md-sys-typescale-label-small-weight);
  line-height: var(--md-sys-typescale-label-small-line-height);
}

/* 通用材料组件样式 */
.md-surface {
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
}

.md-surface-variant {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
}

.md-primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-primary-container {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.md-secondary {
  background-color: var(--md-sys-color-secondary);
  color: var(--md-sys-color-on-secondary);
}

.md-secondary-container {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.md-tertiary {
  background-color: var(--md-sys-color-tertiary);
  color: var(--md-sys-color-on-tertiary);
}

.md-tertiary-container {
  background-color: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
}

/* 卡片组件 */
.md-card {
  background-color: var(--md-sys-color-surface-container-low);
  color: var(--md-sys-color-on-surface);
  border-radius: var(--md-sys-shape-corner-medium);
  box-shadow: var(--md-sys-elevation-level1);
  transition: box-shadow var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.md-card:hover {
  box-shadow: var(--md-sys-elevation-level2);
}

.md-card-elevated {
  box-shadow: var(--md-sys-elevation-level2);
}

.md-card-filled {
  background-color: var(--md-sys-color-surface-container-highest);
}

.md-card-outlined {
  background-color: var(--md-sys-color-surface);
  border: 1px solid var(--md-sys-color-outline-variant);
  box-shadow: none;
}

/* 按钮组件 */
.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-sm);
  min-height: 40px;
  padding: 0 var(--md-sys-spacing-2xl);
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  position: relative;
  overflow: hidden;
}

.md-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: currentColor;
  opacity: 0;
  transition: opacity var(--md-sys-motion-duration-short1) var(--md-sys-motion-easing-standard);
}

.md-button:hover::before {
  opacity: 0.08;
}

.md-button:focus::before {
  opacity: 0.12;
}

.md-button:active::before {
  opacity: 0.12;
}

.md-button-filled {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-button-outlined {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
}

.md-button-text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

.md-button-tonal {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

/* FAB按钮 */
.md-fab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border: none;
  border-radius: var(--md-sys-shape-corner-large);
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  box-shadow: var(--md-sys-elevation-level3);
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  position: relative;
  overflow: hidden;
}

.md-fab:hover {
  box-shadow: var(--md-sys-elevation-level4);
}

.md-fab-small {
  width: 40px;
  height: 40px;
}

.md-fab-large {
  width: 96px;
  height: 96px;
}

.md-fab-extended {
  width: auto;
  min-width: 80px;
  height: 56px;
  padding: 0 var(--md-sys-spacing-lg);
  gap: var(--md-sys-spacing-lg);
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
}

/* 输入框组件 */
.md-text-field {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  min-width: 280px;
}

.md-text-field-input {
  width: 100%;
  padding: var(--md-sys-spacing-lg);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-extra-small);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
  transition: border-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.md-text-field-input:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  border-width: 2px;
}

.md-text-field-filled .md-text-field-input {
  background-color: var(--md-sys-color-surface-container-highest);
  border: none;
  border-bottom: 1px solid var(--md-sys-color-on-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small) var(--md-sys-shape-corner-extra-small) 0 0;
}

.md-text-field-filled .md-text-field-input:focus {
  border-bottom: 2px solid var(--md-sys-color-primary);
}

/* 通用动画类 */
.md-motion-emphasis-enter {
  animation: md-emphasis-enter var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-emphasized);
}

.md-motion-emphasis-exit {
  animation: md-emphasis-exit var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-emphasized-accelerate);
}

.md-motion-fade-in {
  animation: md-fade-in var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-motion-fade-out {
  animation: md-fade-out var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.md-motion-slide-in {
  animation: md-slide-in var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-emphasized-decelerate);
}

.md-motion-slide-out {
  animation: md-slide-out var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-emphasized-accelerate);
}

/* 动画关键帧 */
@keyframes md-emphasis-enter {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes md-emphasis-exit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes md-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes md-fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes md-slide-in {
  0% {
    opacity: 0;
    transform: translateY(16px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes md-slide-out {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-16px);
  }
}

/* 主题切换按钮 */
.theme-toggle {
  position: fixed;
  top: var(--md-sys-spacing-2xl);
  right: var(--md-sys-spacing-2xl);
  z-index: 1000;
}