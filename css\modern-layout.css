/* 现代化布局样式 - Material Design 3 优化居中版本 */

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
  background-color: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
  transition: background-color var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard),
              color var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 现代化导航栏 - 完美居中 */
.modern-nav {
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .modern-nav {
  background-color: rgba(18, 18, 18, 0.9);
}

.nav-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-lg) 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-md);
}

.brand-icon {
  font-size: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-text {
  font-weight: 600;
  color: var(--md-sys-color-primary);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-lg);
}

/* Hero区域 - 完美居中 */
.hero-section {
  position: relative;
  min-height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-align: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(2, 119, 189, 0.08) 0%, 
    rgba(38, 166, 154, 0.08) 35%,
    rgba(123, 31, 162, 0.08) 70%,
    rgba(2, 119, 189, 0.08) 100%);
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, var(--md-sys-color-primary) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, var(--md-sys-color-secondary) 1px, transparent 1px);
  background-size: 60px 60px;
  background-position: 0 0, 30px 30px;
  opacity: 0.03;
}

.hero-content {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: var(--md-sys-spacing-6xl) var(--md-sys-spacing-2xl);
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--md-sys-spacing-6xl);
  align-items: center;
}

.hero-text {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-2xl);
  text-align: left;
}

.hero-title {
  background: linear-gradient(135deg, 
    var(--md-sys-color-primary),
    var(--md-sys-color-secondary),
    var(--md-sys-color-tertiary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.hero-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  font-weight: 400;
  max-width: 600px;
}

.hero-features {
  display: flex;
  gap: var(--md-sys-spacing-2xl);
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-sm);
  padding: var(--md-sys-spacing-sm) var(--md-sys-spacing-lg);
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-full);
  transition: transform var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.feature-item:hover {
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 20px;
}

/* 搜索卡片 - 居中对齐 */
.search-card {
  width: 420px;
  padding: var(--md-sys-spacing-3xl);
  text-align: center;
}

.search-header {
  margin-bottom: var(--md-sys-spacing-2xl);
}

.search-header h3 {
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-sm);
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-lg);
  align-items: stretch;
}

.search-form .md-text-field {
  width: 100%;
}

.search-btn {
  height: 56px;
  white-space: nowrap;
  align-self: center;
  min-width: 160px;
}

/* 统计卡片区域 - 完美居中网格 */
.stats-section {
  padding: var(--md-sys-spacing-6xl) 0;
}

.stats-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--md-sys-spacing-2xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--md-sys-spacing-2xl);
  justify-items: center;
}

.stat-card {
  width: 100%;
  max-width: 320px;
  padding: var(--md-sys-spacing-3xl);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-lg);
  text-align: center;
  transition: transform var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-icon {
  font-size: 48px;
  opacity: 0.8;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--md-sys-spacing-xs);
}

.stat-number {
  color: var(--md-sys-color-primary);
  font-weight: 700;
}

.stat-label {
  color: var(--md-sys-color-on-surface-variant);
}

/* 主要内容区域 - 居中网格布局 - 宽屏优化 */
.main-container {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4xl) var(--md-sys-spacing-2xl);
  flex: 1;
}

.content-grid {
  display: grid;
  grid-template-columns: 480px 1fr;
  gap: var(--md-sys-spacing-4xl);
  min-height: 800px;
  align-items: start;
  max-width: none;
  width: 100%;
}

/* 侧边栏 - 垂直居中内容 - 视觉优化版本 - 加宽版 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-2xl);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-2xl);
  height: fit-content;
  position: sticky;
  top: calc(64px + var(--md-sys-spacing-2xl));
  background: linear-gradient(145deg, 
    var(--md-sys-color-surface-container-low) 0%,
    var(--md-sys-color-surface) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid var(--md-sys-color-outline-variant);
  width: 100%;
  min-width: 480px;
}

.sidebar .md-card {
  padding: var(--md-sys-spacing-3xl);
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

[data-theme="dark"] .sidebar .md-card {
  background: rgba(30, 30, 30, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--md-sys-spacing-lg);
  text-align: center;
}

.card-header h3 {
  color: var(--md-sys-color-primary);
  flex: 1;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-lg);
}

/* 信息项 - 居中对齐 - 加大版本 */
.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-md);
  text-align: center;
  padding: var(--md-sys-spacing-2xl);
  background: linear-gradient(145deg, 
    var(--md-sys-color-surface-container-low) 0%,
    var(--md-sys-color-surface-container) 100%);
  border-radius: var(--md-sys-shape-corner-medium);
  border: 1px solid var(--md-sys-color-outline-variant);
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--md-sys-elevation-level2);
}

.info-label {
  color: var(--md-sys-color-primary);
  font-weight: 700;
  font-size: var(--md-sys-typescale-title-small-size);
}

.info-text {
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-body-medium-size);
  line-height: 1.6;
}

/* 筛选按钮 - 居中网格 - 精美化版本 - 大尺寸 */
.filter-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--md-sys-spacing-lg);
  justify-items: center;
}

.filter-button {
  width: 100%;
  padding: var(--md-sys-spacing-xl) var(--md-sys-spacing-2xl);
  border: 2px solid transparent;
  border-radius: var(--md-sys-shape-corner-full);
  background: linear-gradient(145deg, 
    var(--md-sys-color-surface) 0%, 
    var(--md-sys-color-surface-container) 100%);
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-title-small-size);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-emphasized);
  text-align: center;
  white-space: nowrap;
  min-height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, 
    var(--md-sys-color-primary) 0%,
    var(--md-sys-color-secondary) 100%);
  opacity: 0;
  transition: opacity var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  z-index: -1;
}

.filter-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(2, 119, 189, 0.15);
  border-color: var(--md-sys-color-primary);
}

.filter-button:hover::before {
  opacity: 0.05;
}

.filter-button.active {
  background: linear-gradient(145deg, 
    var(--md-sys-color-primary) 0%,
    var(--md-sys-color-secondary) 100%);
  color: var(--md-sys-color-on-primary);
  border-color: var(--md-sys-color-primary);
  box-shadow: 0 4px 16px rgba(2, 119, 189, 0.3);
  transform: translateY(-1px);
}

.filter-button.active::before {
  opacity: 0;
}

/* 深色模式下的筛选按钮 */
[data-theme="dark"] .filter-button {
  background: linear-gradient(145deg, 
    var(--md-sys-color-surface-container) 0%, 
    var(--md-sys-color-surface-variant) 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .filter-button:hover {
  box-shadow: 0 6px 20px rgba(129, 212, 250, 0.2);
}

/* 医院列表 - 居中卡片 - 加宽版 */
.hospital-list {
  max-height: 600px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-lg);
}

.hospital-item {
  padding: var(--md-sys-spacing-2xl);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  background-color: var(--md-sys-color-surface);
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  text-align: center;
}

.hospital-item:hover {
  border-color: var(--md-sys-color-primary);
  box-shadow: var(--md-sys-elevation-level2);
  transform: translateY(-2px);
}

.hospital-name {
  font-weight: 600;
  color: var(--md-sys-color-on-surface);
  margin-bottom: var(--md-sys-spacing-md);
  font-size: var(--md-sys-typescale-title-medium-size);
}

.hospital-specialty {
  color: var(--md-sys-color-primary);
  font-size: var(--md-sys-typescale-body-medium-size);
  margin-bottom: var(--md-sys-spacing-md);
  padding: var(--md-sys-spacing-sm) var(--md-sys-spacing-lg);
  background-color: var(--md-sys-color-primary-container);
  border-radius: var(--md-sys-shape-corner-full);
  display: inline-block;
  font-weight: 500;
}

.hospital-location {
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-body-medium-size);
}

/* 地图区域 - 完美居中 - 视觉增强版 */
.map-section {
  display: flex;
  flex-direction: column;
}

.map-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 700px;
  height: 700px;
  background: linear-gradient(145deg, 
    var(--md-sys-color-surface) 0%,
    var(--md-sys-color-surface-container-low) 100%);
  border: 1px solid var(--md-sys-color-outline-variant);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  transition: box-shadow var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.map-card:hover {
  box-shadow: 0 8px 32px rgba(2, 119, 189, 0.12);
}

.map-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--md-sys-spacing-2xl);
  border-bottom: 2px solid var(--md-sys-color-primary);
  text-align: center;
  background: linear-gradient(135deg, 
    rgba(2, 119, 189, 0.02) 0%,
    rgba(38, 166, 154, 0.02) 100%);
}

.map-header h3 {
  color: var(--md-sys-color-primary);
  flex: 1;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-sm);
}

.map-controls {
  display: flex;
  gap: var(--md-sys-spacing-sm);
}

.map-container {
  flex: 1 !important;
  position: relative !important;
  min-height: 600px !important;
  height: 100% !important;
  width: 100% !important;
  display: block !important;
  overflow: hidden !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.map-container::before {
  display: none !important;
}

#map {
  width: 100% !important;
  height: 100% !important;
  min-height: 600px !important;
  border-radius: 0 0 var(--md-sys-shape-corner-medium) var(--md-sys-shape-corner-medium) !important;
  position: relative !important;
  z-index: 1 !important;
}

.loading-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-lg);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--md-sys-color-outline-variant);
  border-top: 4px solid var(--md-sys-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主题切换按钮 - 固定居中位置 */
.theme-toggle {
  position: fixed;
  top: var(--md-sys-spacing-2xl);
  right: var(--md-sys-spacing-2xl);
  z-index: 1001;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 深色模式特殊处理 */
[data-theme="dark"] .hero-gradient {
  background: linear-gradient(135deg, 
    rgba(129, 212, 250, 0.05) 0%, 
    rgba(38, 166, 154, 0.05) 35%,
    rgba(123, 31, 162, 0.05) 70%,
    rgba(129, 212, 250, 0.05) 100%);
}

[data-theme="dark"] .hero-pattern {
  opacity: 0.02;
}

[data-theme="dark"] .hero-title {
  background: linear-gradient(135deg, 
    var(--md-sys-color-primary),
    var(--md-sys-color-secondary),
    #E1BEE7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 现代化页脚 */
.modern-footer {
  margin-top: var(--md-sys-spacing-6xl);
  padding: var(--md-sys-spacing-6xl) 0 var(--md-sys-spacing-4xl);
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

.modern-footer .footer-content {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 var(--md-sys-spacing-2xl);
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--md-sys-spacing-4xl);
  margin-bottom: var(--md-sys-spacing-4xl);
}

.footer-section h4 {
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-lg);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-md);
}

.footer-links a {
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  transition: color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.footer-links a:hover {
  color: var(--md-sys-color-primary);
}

.footer-links li {
  color: var(--md-sys-color-on-surface-variant);
}

.social-links {
  display: flex;
  gap: var(--md-sys-spacing-lg);
  flex-wrap: wrap;
}

.footer-bottom {
  padding-top: var(--md-sys-spacing-4xl);
  border-top: 1px solid var(--md-sys-color-outline-variant);
  text-align: center;
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-sm);
}

.footer-info p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

/* 响应式设计扩展 */
@media (max-width: 640px) {
  .modern-footer {
    padding: var(--md-sys-spacing-4xl) 0 var(--md-sys-spacing-2xl);
  }
  
  .modern-footer .footer-content {
    padding: 0 var(--md-sys-spacing-lg);
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-2xl);
  }
  
/* 超宽屏幕优化 */
@media (min-width: 1920px) {
  .main-container {
    padding: var(--md-sys-spacing-4xl) var(--md-sys-spacing-6xl);
  }
  
  .content-grid {
    grid-template-columns: 600px 1fr;
    gap: var(--md-sys-spacing-6xl);
  }
  
  .sidebar {
    min-width: 600px;
  }
  
  .filter-buttons {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }
  
  .filter-button {
    padding: var(--md-sys-spacing-2xl) var(--md-sys-spacing-3xl);
    min-height: 64px;
    font-size: var(--md-sys-typescale-title-medium-size);
  }
  
  .hospital-item {
    padding: var(--md-sys-spacing-3xl);
  }
  
  .hospital-name {
    font-size: var(--md-sys-typescale-title-large-size);
  }
}

/* 响应式设计 - 确保所有屏幕尺寸下的完美居中 - 宽屏优化 */
@media (max-width: 1600px) {
  .main-container {
    padding: var(--md-sys-spacing-4xl) var(--md-sys-spacing-xl);
  }
}

@media (max-width: 1400px) {
  .nav-content,
  .hero-content,
  .stats-container {
    max-width: 1200px;
  }
  
  .content-grid {
    grid-template-columns: 420px 1fr;
    gap: var(--md-sys-spacing-3xl);
  }
}

@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 380px 1fr;
    gap: var(--md-sys-spacing-2xl);
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-4xl);
    text-align: center;
  }
  
  .hero-text {
    text-align: center;
    align-items: center;
  }
  
  .hero-features {
    justify-content: center;
  }
  
  .search-card {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }
  
  .filter-buttons {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  }
}

@media (max-width: 968px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-2xl);
  }
  
  .sidebar {
    order: 2;
    position: static;
    max-width: none;
    min-width: auto;
    width: 100%;
  }
  
  .map-section {
    order: 1;
  }
  
  .hero-content {
    padding: var(--md-sys-spacing-4xl) var(--md-sys-spacing-lg);
  }
  
  .hero-features {
    flex-direction: column;
    align-items: center;
    gap: var(--md-sys-spacing-lg);
  }
  
  .search-form {
    align-items: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--md-sys-spacing-lg);
  }
  
  .filter-buttons {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  }
}

@media (max-width: 768px) {
  .nav-content {
    padding: var(--md-sys-spacing-md) var(--md-sys-spacing-lg);
    flex-wrap: wrap;
    gap: var(--md-sys-spacing-md);
  }
  
  .nav-actions {
    gap: var(--md-sys-spacing-sm);
    order: 3;
    width: 100%;
    justify-content: center;
    margin-top: var(--md-sys-spacing-sm);
  }
  
  .nav-brand {
    order: 1;
  }
  
  .user-status {
    order: 2;
    margin-left: auto;
  }
  
  .hero-section {
    min-height: 60vh;
    padding: var(--md-sys-spacing-2xl) 0;
  }
  
  .hero-title {
    font-size: var(--md-sys-typescale-display-small-size);
  }
  
  .hero-subtitle {
    font-size: var(--md-sys-typescale-title-large-size);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    padding: var(--md-sys-spacing-2xl);
  }
  
  .stat-icon {
    font-size: 40px;
    margin-bottom: var(--md-sys-spacing-sm);
  }
  
  .main-container {
    padding: var(--md-sys-spacing-2xl) var(--md-sys-spacing-lg);
  }
  
  .map-card {
    min-height: 500px;
  }
}

@media (max-width: 640px) {
  .hero-content {
    padding: var(--md-sys-spacing-2xl) var(--md-sys-spacing-md);
  }
  
  .hero-title {
    font-size: var(--md-sys-typescale-headline-large-size);
    line-height: 1.2;
  }
  
  .hero-subtitle {
    font-size: var(--md-sys-typescale-title-medium-size);
  }
  
  .search-card {
    padding: var(--md-sys-spacing-2xl);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
    margin: 0 auto;
    justify-items: center;
  }
  
  .stat-card {
    max-width: 100%;
    width: 100%;
  }
  
  .content-grid {
    gap: var(--md-sys-spacing-lg);
  }
  
  .sidebar {
    padding: var(--md-sys-spacing-lg);
  }
  
  .sidebar .md-card {
    padding: var(--md-sys-spacing-lg);
  }
  
  .filter-buttons {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: var(--md-sys-spacing-xs);
  }
  
  .filter-button {
    padding: var(--md-sys-spacing-sm);
    font-size: var(--md-sys-typescale-label-small-size);
    min-height: 36px;
  }
  
  .hospital-list {
    max-height: 400px;
  }
  
  .map-card {
    min-height: 400px;
  }
  
  .map-header {
    padding: var(--md-sys-spacing-lg);
    flex-direction: column;
    gap: var(--md-sys-spacing-md);
    text-align: center;
  }
  
  .map-controls {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .brand-text {
    font-size: var(--md-sys-typescale-title-small-size);
  }
  
  .hero-section {
    min-height: 50vh;
  }
  
  .hero-title {
    font-size: var(--md-sys-typescale-headline-medium-size);
  }
  
  .hero-subtitle {
    font-size: var(--md-sys-typescale-body-large-size);
  }
  
  .feature-item {
    padding: var(--md-sys-spacing-xs) var(--md-sys-spacing-md);
    font-size: var(--md-sys-typescale-body-small-size);
  }
  
  .search-card {
    padding: var(--md-sys-spacing-lg);
  }
  
  .search-btn {
    height: 48px;
    min-width: 120px;
  }
  
  .main-container {
    padding: var(--md-sys-spacing-lg) var(--md-sys-spacing-md);
  }
  
  .theme-toggle {
    top: var(--md-sys-spacing-lg);
    right: var(--md-sys-spacing-lg);
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
}

/* 高度优化 - 确保内容在小屏幕上也能很好地居中 */
@media (max-height: 600px) {
  .hero-section {
    min-height: auto;
    padding: var(--md-sys-spacing-4xl) 0;
  }
  
  .stats-section {
    padding: var(--md-sys-spacing-4xl) 0;
  }
  
  .main-container {
    padding: var(--md-sys-spacing-2xl) var(--md-sys-spacing-2xl);
  }
}

/* 极小屏幕优化 */
@media (max-width: 320px) {
  .nav-content {
    padding: var(--md-sys-spacing-sm) var(--md-sys-spacing-md);
  }
  
  .hero-content {
    padding: var(--md-sys-spacing-lg) var(--md-sys-spacing-sm);
  }
  
  .stats-container,
  .main-container {
    padding-left: var(--md-sys-spacing-sm);
    padding-right: var(--md-sys-spacing-sm);
  }
  
  .search-card {
    padding: var(--md-sys-spacing-md);
  }
  
  .filter-buttons {
    grid-template-columns: 1fr 1fr;
  }
  
  .filter-button {
    font-size: var(--md-sys-typescale-label-small-size);
    padding: var(--md-sys-spacing-xs) var(--md-sys-spacing-sm);
  }
}
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-4xl);
    text-align: center;
  }
  
  .hero-features {
    justify-content: center;
  }
}

@media (max-width: 968px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-lg);
  }
  
  .sidebar {
    order: 2;
  }
  
  .map-section {
    order: 1;
  }
  
  .hero-content {
    padding: var(--md-sys-spacing-4xl) var(--md-sys-spacing-lg);
  }
  
  .hero-features {
    flex-direction: column;
    align-items: center;
    gap: var(--md-sys-spacing-lg);
  }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--md-sys-spacing-lg);
  }
}

@media (max-width: 640px) {
  .nav-content {
    padding: var(--md-sys-spacing-lg);
  }
  
  .nav-actions {
    gap: var(--md-sys-spacing-sm);
  }
  
  .nav-actions .md-button-text,
  .nav-actions .md-button-outlined {
    padding: var(--md-sys-spacing-sm) var(--md-sys-spacing-lg);
    font-size: var(--md-sys-typescale-label-medium-size);
  }
  
  .hero-title {
    font-size: var(--md-sys-typescale-display-small-size);
  }
  
  .hero-subtitle {
    font-size: var(--md-sys-typescale-title-large-size);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .main-container {
    padding: var(--md-sys-spacing-2xl) var(--md-sys-spacing-lg);
  }
}