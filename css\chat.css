/* 实时聊天功能样式 */
.chat-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 320px;
    height: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    z-index: 1000;
    transition: all 0.3s ease;
}

.chat-container.minimized {
    height: 50px;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.chat-title {
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-online-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.chat-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.3s;
}

.chat-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.chat-message {
    display: flex;
    gap: 8px;
    animation: slideInUp 0.3s ease;
}

.chat-message.own {
    flex-direction: row-reverse;
}

.chat-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 12px;
    flex-shrink: 0;
}

.chat-message-content {
    flex: 1;
    max-width: 70%;
}

.chat-message.own .chat-message-content {
    text-align: right;
}

.chat-message-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 2px;
    font-size: 11px;
    color: #666;
}

.chat-message.own .chat-message-header {
    justify-content: flex-end;
}

.chat-username {
    font-weight: 600;
}

.chat-user-type {
    background: #e9ecef;
    padding: 1px 6px;
    border-radius: 8px;
    font-size: 10px;
}

.chat-user-type.doctor {
    background: #d4edda;
    color: #155724;
}

.chat-user-type.researcher {
    background: #d1ecf1;
    color: #0c5460;
}

.chat-user-type.admin {
    background: #f8d7da;
    color: #721c24;
}

.chat-user-type.ai_assistant {
    background: #e8f5e8;
    color: #2e7d32;
}

.chat-timestamp {
    font-size: 10px;
    color: #999;
}

.chat-message-text {
    background: white;
    padding: 8px 12px;
    border-radius: 12px;
    font-size: 13px;
    line-height: 1.4;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
}

.chat-message.own .chat-message-text {
    background: #667eea;
    color: white;
}

.chat-message.system .chat-message-text {
    background: #e9ecef;
    color: #6c757d;
    font-style: italic;
    text-align: center;
}

/* AI消息特殊样式 */
.chat-message.ai-message {
    margin: 8px 0;
}

.chat-message.ai-message .chat-avatar.ai-avatar {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    border: 2px solid #e8f5e8;
    animation: aiGlow 2s ease-in-out infinite alternate;
}

.chat-message.ai-message .chat-username.ai-username {
    color: #2e7d32;
    font-weight: 700;
}

.chat-message.ai-message .chat-message-text.ai-text {
    background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
    border: 1px solid #c8e6c9;
    color: #1b5e20;
    position: relative;
}

.chat-message.ai-message .chat-message-text.ai-text::before {
    content: "🤖";
    position: absolute;
    top: -8px;
    left: -8px;
    background: #4CAF50;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chat-input-container {
    padding: 12px;
    border-top: 1px solid #e0e0e0;
    background: white;
    border-radius: 0 0 12px 12px;
}

.chat-input-form {
    display: flex;
    gap: 8px;
}

.chat-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 13px;
    outline: none;
    transition: border-color 0.3s;
}

.chat-input:focus {
    border-color: #667eea;
}

.chat-send-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s;
    font-size: 14px;
}

.chat-send-btn:hover {
    background: #5a6fd8;
}

.chat-send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 聊天触发按钮 */
.chat-trigger {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    z-index: 999;
}

.chat-trigger:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.chat-trigger.has-new-message {
    animation: pulse 2s infinite;
}

/* 在线用户列表 */
.chat-online-users {
    padding: 8px 12px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    font-size: 11px;
    color: #666;
}

.chat-online-users-list {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-top: 4px;
}

.chat-online-user {
    display: flex;
    align-items: center;
    gap: 4px;
    background: white;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 10px;
}

.chat-online-avatar {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 8px;
    font-weight: 600;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    50% {
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    100% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

@keyframes aiGlow {
    0% {
        box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }
    100% {
        box-shadow: 0 0 15px rgba(76, 175, 80, 0.6);
    }
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-10px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-container {
        width: calc(100vw - 40px);
        height: 350px;
        bottom: 10px;
        right: 20px;
        left: 20px;
    }
    
    .chat-trigger {
        bottom: 10px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .chat-container {
        width: calc(100vw - 20px);
        height: 300px;
        bottom: 5px;
        right: 10px;
        left: 10px;
    }
    
    .chat-trigger {
        bottom: 5px;
        right: 15px;
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .chat-message-content {
        max-width: 80%;
    }
}
