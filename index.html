<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地域医疗专长地图 - 发现身边的医疗专家</title>
    
    <!-- Material Design 3 设计系统 -->
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/modern-layout.css">
    
    <!-- 模块化CSS文件 -->
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/header.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/hospital-cards.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="stylesheet" href="css/enhancements.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/user-status.css">
    <link rel="stylesheet" href="css/chat.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="md-surface">
    <!-- 主题切换按钮 -->
    <button class="md-fab theme-toggle" id="themeToggle" title="切换深色模式">
        <span class="material-icons">🌙</span>
    </button>
    
    <!-- 现代化导航栏 -->
    <nav class="modern-nav md-surface-container">
        <div class="nav-content">
            <div class="nav-brand">
                <div class="brand-icon">🏥</div>
                <span class="brand-text headline-small">医疗专长地图</span>
            </div>
            <div class="nav-actions">
                <button class="md-button-text" onclick="scrollToSection('about')">关于项目</button>
                <button class="md-button-text" onclick="scrollToSection('contact')">联系我们</button>
                <button class="md-button-outlined" onclick="window.open('https://github.com', '_blank')">GitHub</button>
                <!-- 用户状态将通过JavaScript动态添加到这里 -->
                <div class="user-status" id="userStatus"></div>
            </div>
        </div>
    </nav>

    <!-- Hero区域 -->
    <section class="hero-section md-surface">
        <div class="hero-background">
            <div class="hero-gradient"></div>
            <div class="hero-pattern"></div>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="display-medium hero-title">发现身边的医疗专家</h1>
                <p class="headline-small hero-subtitle">基于地理环境特色的医疗专科资源分布可视化平台</p>
                <div class="hero-features">
                    <div class="feature-item">
                        <span class="feature-icon">🎯</span>
                        <span class="body-large">精准匹配专科</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">📍</span>
                        <span class="body-large">地理位置优化</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🤖</span>
                        <span class="body-large">AI智能推荐</span>
                    </div>
                </div>
            </div>
            <div class="hero-search">
                <div class="search-card md-card">
                    <div class="search-header">
                        <h3 class="title-large">🔍 智能搜索</h3>
                        <p class="body-medium">输入症状、医院或地区快速查找</p>
                    </div>
                    <div class="search-form">
                        <div class="md-text-field md-text-field-filled">
                            <input type="text" class="md-text-field-input" placeholder="请输入医院名称、专科或地区..." id="searchInput">
                        </div>
                        <button class="md-button-filled search-btn">
                            <span class="material-icons">🔍</span>
                            <span>搜索</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 数据统计卡片区域 -->
    <section class="stats-section md-surface-container-low">
        <div class="stats-container">
            <div class="stats-grid">
                <div class="stat-card md-card md-card-filled md-motion-slide-in">
                    <div class="stat-icon">🏥</div>
                    <div class="stat-content">
                        <div class="stat-number display-small" id="totalHospitals">0</div>
                        <div class="stat-label body-large">医疗机构</div>
                    </div>
                </div>
                <div class="stat-card md-card md-card-filled md-motion-slide-in">
                    <div class="stat-icon">🎯</div>
                    <div class="stat-content">
                        <div class="stat-number display-small" id="totalSpecialties">0</div>
                        <div class="stat-label body-large">专科领域</div>
                    </div>
                </div>
                <div class="stat-card md-card md-card-filled md-motion-slide-in">
                    <div class="stat-icon">📍</div>
                    <div class="stat-content">
                        <div class="stat-number display-small" id="totalProvinces">0</div>
                        <div class="stat-label body-large">覆盖省份</div>
                    </div>
                </div>
                <div class="stat-card md-card md-card-filled md-motion-slide-in">
                    <div class="stat-icon">🌆</div>
                    <div class="stat-content">
                        <div class="stat-number display-small" id="totalCities">0</div>
                        <div class="stat-label body-large">覆盖城市</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要内容区域 -->
    <main class="main-container">
        <div class="content-grid">
            <!-- 侧边栏 -->
            <aside class="sidebar md-surface-container">
                <!-- 项目说明卡片 -->
                <div class="info-card md-card md-motion-fade-in">
                    <div class="card-header">
                        <h3 class="title-large">💡 项目理念</h3>
                    </div>
                    <div class="card-content">
                        <div class="info-item">
                            <div class="info-label label-large">理念</div>
                            <div class="info-text body-medium">地域环境造就医疗专长，如云南擅长蘑菇中毒救治</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label label-large">目标</div>
                            <div class="info-text body-medium">帮助患者找到最适合的专科医院和医疗资源</div>
                        </div>
                    </div>
                </div>

                <!-- 专科筛选卡片 -->
                <div class="filter-card md-card md-motion-fade-in">
                    <div class="card-header">
                        <h3 class="title-large">🎯 专科筛选</h3>
                    </div>
                    <div class="card-content">
                        <div class="filter-buttons" id="filterButtons">
                            <!-- 筛选按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 医院列表卡片 -->
                <div class="hospital-card md-card md-motion-fade-in">
                    <div class="card-header">
                        <h3 class="title-large">🏥 医院列表</h3>
                        <button class="md-button-text" onclick="toggleListView()">切换视图</button>
                    </div>
                    <div class="card-content">
                        <div class="hospital-list" id="hospitalList">
                            <!-- 医院列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 地图区域 -->
            <section class="map-section">
                <div class="map-card md-card">
                    <div class="map-header">
                        <h3 class="title-large">🗺️ 医疗专长地图</h3>
                        <div class="map-controls">
                            <button class="md-fab md-fab-small" id="locateBtn" title="定位到我的位置">
                                <span class="material-icons">📍</span>
                            </button>
                            <button class="md-fab md-fab-small" id="fullscreenBtn" title="全屏显示">
                                <span class="material-icons">⛶</span>
                            </button>
                        </div>
                    </div>
                    <div class="map-container">
                        <div id="map" class="loading">
                            <div class="loading-content">
                                <div class="loading-spinner"></div>
                                <p class="body-large">正在加载地图...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 关于项目区域 -->
    <section id="about" class="about-section md-surface-container-low">
        <div class="about-container">
            <h2 class="display-small">关于项目</h2>
            <div class="about-content">
                <p class="body-large">地域医疗专长地图是一个创新的医疗资源可视化平台，旨在帮助患者发现身边具有特色医疗专长的医院。</p>
                <p class="body-medium">我们相信地域环境造就医疗专长，比如云南擅长蘑菇中毒救治、西藏专精高原病治疗等。通过这个平台，患者可以更精准地找到最适合的专科医院和医疗资源。</p>
            </div>
        </div>
    </section>

    <!-- 项目页脚 -->
    <footer id="contact" class="gov-footer">
        <div class="footer-content">
            <div class="footer-sections">
                <div class="footer-section">
                    <h4>项目信息</h4>
                    <ul>
                        <li><a href="#">项目介绍</a></li>
                        <li><a href="#">数据来源</a></li>
                        <li><a href="#">使用说明</a></li>
                        <li><a href="#">版本更新</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>技术支持</h4>
                    <ul>
                        <li><a href="#">技术文档</a></li>
                        <li><a href="#">API接口</a></li>
                        <li><a href="#">问题反馈</a></li>
                        <li><a href="#">建议提交</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <ul>
                        <li>邮箱：<EMAIL></li>
                        <li>GitHub：github.com/yourname</li>
                        <li>微信：your-wechat-id</li>
                        <li>QQ：your-qq-number</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#" class="social-link">项目主页</a>
                        <a href="#" class="social-link">开源代码</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-info">
                    <p>个人开发项目 | 数据来源于公开资料整理 | 仅供学习交流使用</p>
                    <p>如有疑问请联系作者 | 持续更新维护中 ❤️</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 外部依赖 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>
    
    <!-- 模块化JavaScript文件 -->
    <script src="js/modern-ui.js"></script>
    <script src="js/modern-helpers.js"></script>
    <script src="js/auth-manager.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/map-manager.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/chat-manager.js"></script>
    <script src="js/app.js"></script>

    <!-- Google Maps API -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY&callback=initMapCallback&language=zh-CN&libraries=visualization">
    </script>
</body>
</html>