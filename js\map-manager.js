// 地图管理模块
class MapManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.map = null;
        this.markers = [];
        this.heatmap = null;
        this.connections = [];
        this.showHeatmap = false;
        this.showConnections = false;
    }

    initMap() {
        // 确保地图容器已准备好
        const mapContainer = document.getElementById("map");
        if (!mapContainer) {
            console.error('地图容器未找到');
            return null;
        }

        this.map = new google.maps.Map(mapContainer, {
            zoom: 5,
            center: { lat: 35.8617, lng: 104.1954 }, // 中国中心点
            styles: this.getMapStyles()
        });

        // 等待地图容器完全渲染后触发resize
        setTimeout(() => {
            if (this.map) {
                google.maps.event.trigger(this.map, 'resize');
                // 重新设置中心点确保地图正确显示
                this.map.setCenter({ lat: 35.8617, lng: 104.1954 });
            }
        }, 500);

        this.createMarkers();
        this.setupMapControls();
        return this.map;
    }

    getMapStyles() {
        return [
            {
                featureType: "all",
                elementType: "geometry.fill",
                stylers: [{ weight: "2.00" }]
            },
            {
                featureType: "all",
                elementType: "geometry.stroke",
                stylers: [{ color: "#9c9c9c" }]
            },
            {
                featureType: "all",
                elementType: "labels.text",
                stylers: [{ visibility: "on" }]
            },
            {
                featureType: "water",
                elementType: "geometry.fill",
                stylers: [{ color: "#a2daf2" }]
            }
        ];
    }

    createMarkers() {
        this.clearMarkers();
        
        const data = this.dataManager.getFilteredData();
        data.forEach(hospital => {
            const marker = new google.maps.Marker({
                position: hospital.coordinates,
                map: this.map,
                title: hospital.name,
                icon: this.getMarkerIcon(hospital.category),
                animation: google.maps.Animation.DROP
            });

            const infoWindow = new google.maps.InfoWindow({
                content: this.createInfoWindowContent(hospital)
            });

            marker.addListener('click', () => {
                this.closeAllInfoWindows();
                infoWindow.open(this.map, marker);
                marker.infoWindow = infoWindow;
            });

            this.markers.push(marker);
        });
    }

    createInfoWindowContent(hospital) {
        return `
            <div class="info-window">
                <div class="info-title">${hospital.name}</div>
                <div class="info-specialty">${hospital.specialty}</div>
                <div class="info-note">${hospital.notes}</div>
                <div style="margin-top: 12px;">
                    <button onclick="app.uiManager.showHospitalDetails('${hospital.id}')" 
                            style="background: #0052cc; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">
                        查看详情
                    </button>
                </div>
            </div>
        `;
    }

    getMarkerIcon(category) {
        const iconMap = {
            '中毒救治': this.createSVGIcon('☠️', '#ff6b6b', '#ee5a24'),
            '高原病': this.createSVGIcon('🏔️', '#4CAF50', '#388e3c'),
            '包虫病': this.createSVGIcon('🦠', '#FF9800', '#f57c00'),
            '职业病': this.createSVGIcon('🏭', '#2196F3', '#1976d2'),
            '海洋生物': this.createSVGIcon('🌊', '#00BCD4', '#0097a7'),
            '其他': this.createSVGIcon('🏥', '#9E9E9E', '#757575')
        };
        return iconMap[category] || iconMap['其他'];
    }

    createSVGIcon(emoji, color1, color2) {
        return {
            url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50">
                    <defs>
                        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:${color1};stop-opacity:1" />
                            <stop offset="100%" style="stop-color:${color2};stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <circle cx="25" cy="25" r="22" fill="url(#grad)" stroke="white" stroke-width="3"/>
                    <text x="25" y="32" text-anchor="middle" fill="white" font-size="18" font-weight="bold">${emoji}</text>
                </svg>
            `)}`,
            scaledSize: new google.maps.Size(50, 50)
        };
    }

    clearMarkers() {
        this.markers.forEach(marker => marker.setMap(null));
        this.markers = [];
    }

    closeAllInfoWindows() {
        this.markers.forEach(marker => {
            if (marker.infoWindow) marker.infoWindow.close();
        });
    }

    // 热力图功能
    toggleHeatmap() {
        this.showHeatmap = !this.showHeatmap;
        
        if (this.showHeatmap) {
            this.createHeatmap();
            this.clearMarkers(); // 隐藏标记
        } else {
            this.clearHeatmap();
            this.createMarkers(); // 显示标记
        }
    }

    createHeatmap() {
        const data = this.dataManager.getFilteredData();
        const heatmapData = data.map(hospital => 
            new google.maps.LatLng(hospital.coordinates.lat, hospital.coordinates.lng)
        );

        this.heatmap = new google.maps.visualization.HeatmapLayer({
            data: heatmapData,
            map: this.map,
            radius: 50,
            opacity: 0.6
        });
    }

    clearHeatmap() {
        if (this.heatmap) {
            this.heatmap.setMap(null);
            this.heatmap = null;
        }
    }

    // 连线功能
    toggleConnections() {
        this.showConnections = !this.showConnections;
        
        if (this.showConnections) {
            this.createConnections();
        } else {
            this.clearConnections();
        }
    }

    createConnections() {
        const data = this.dataManager.getFilteredData();
        const categoryGroups = {};
        
        // 按专科分组
        data.forEach(hospital => {
            if (!categoryGroups[hospital.category]) {
                categoryGroups[hospital.category] = [];
            }
            categoryGroups[hospital.category].push(hospital);
        });

        // 为每个专科创建连线
        Object.values(categoryGroups).forEach(hospitals => {
            if (hospitals.length > 1) {
                this.createConnectionsForCategory(hospitals);
            }
        });
    }

    createConnectionsForCategory(hospitals) {
        const path = hospitals.map(h => h.coordinates);
        
        const polyline = new google.maps.Polyline({
            path: path,
            geodesic: true,
            strokeColor: '#0052cc',
            strokeOpacity: 0.6,
            strokeWeight: 2,
            map: this.map
        });

        this.connections.push(polyline);
    }

    clearConnections() {
        this.connections.forEach(connection => connection.setMap(null));
        this.connections = [];
    }

    // 聚焦到医院
    focusOnHospital(hospitalId) {
        const hospital = this.dataManager.getAllData().find(h => h.id == hospitalId);
        if (hospital && this.map) {
            this.map.setCenter(hospital.coordinates);
            this.map.setZoom(12);
            
            // 找到对应标记并触发点击
            const marker = this.markers.find(m => 
                Math.abs(m.getPosition().lat() - hospital.coordinates.lat) < 0.001 && 
                Math.abs(m.getPosition().lng() - hospital.coordinates.lng) < 0.001
            );
            if (marker) {
                google.maps.event.trigger(marker, 'click');
            }
        }
    }

    updateMarkers() {
        this.createMarkers();
        if (this.showHeatmap) {
            this.clearHeatmap();
            this.createHeatmap();
        }
        if (this.showConnections) {
            this.clearConnections();
            this.createConnections();
        }
    }

    setupMapControls() {
        // 定位按钮
        const locateBtn = document.getElementById('locateBtn');
        if (locateBtn) {
            locateBtn.addEventListener('click', () => {
                this.locateUser();
            });
        }

        // 全屏按钮
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.toggleFullscreen();
            });
        }
    }

    locateUser() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const userLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    this.map.setCenter(userLocation);
                    this.map.setZoom(12);

                    // 添加用户位置标记
                    new google.maps.Marker({
                        position: userLocation,
                        map: this.map,
                        title: '您的位置',
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="8" fill="#4285F4" stroke="white" stroke-width="2"/>
                                    <circle cx="12" cy="12" r="3" fill="white"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(24, 24)
                        }
                    });

                    // 显示提示
                    if (window.app && window.app.uiManager) {
                        window.app.uiManager.showToast('已定位到您的位置', 'success');
                    }
                },
                (error) => {
                    console.error('定位失败:', error);
                    if (window.app && window.app.uiManager) {
                        window.app.uiManager.showToast('定位失败，请检查位置权限', 'error');
                    }
                }
            );
        } else {
            if (window.app && window.app.uiManager) {
                window.app.uiManager.showToast('浏览器不支持定位功能', 'error');
            }
        }
    }

    toggleFullscreen() {
        const mapSection = document.querySelector('.map-section');
        const mapContainer = document.querySelector('.map-container');

        if (!mapSection || !mapContainer) return;

        if (!document.fullscreenElement) {
            mapSection.requestFullscreen().then(() => {
                mapSection.classList.add('fullscreen-mode');
                // 触发地图重绘
                setTimeout(() => {
                    google.maps.event.trigger(this.map, 'resize');
                }, 100);

                if (window.app && window.app.uiManager) {
                    window.app.uiManager.showToast('已进入全屏模式，按ESC退出', 'info');
                }
            }).catch(err => {
                console.error('全屏失败:', err);
            });
        } else {
            document.exitFullscreen();
        }

        // 监听全屏退出
        document.addEventListener('fullscreenchange', () => {
            if (!document.fullscreenElement) {
                mapSection.classList.remove('fullscreen-mode');
                setTimeout(() => {
                    google.maps.event.trigger(this.map, 'resize');
                }, 100);
            }
        });
    }
}

// 导出模块
window.MapManager = MapManager;