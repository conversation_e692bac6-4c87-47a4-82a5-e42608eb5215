// 用户认证管理模块
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.users = this.loadUsers();
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24小时

        // 数据库API配置
        this.useDatabase = true; // 设置为true启用数据库API
        this.apiBaseUrl = 'http://localhost:5000/api';

        this.init();
    }

    init() {
        // 检查是否有保存的登录状态
        this.checkExistingSession();
    }

    // 检查现有会话
    checkExistingSession() {
        const savedSession = localStorage.getItem('medicalMapSession');
        if (savedSession) {
            try {
                const session = JSON.parse(savedSession);
                const now = Date.now();
                
                if (session.expiry > now) {
                    this.currentUser = session.user;
                    this.isLoggedIn = true;
                    
                    // 如果当前在登录页面，跳转到主页
                    if (window.location.pathname.includes('login.html')) {
                        window.location.href = 'index.html';
                    }
                } else {
                    // 会话已过期
                    this.logout();
                }
            } catch (error) {
                console.error('解析会话数据失败:', error);
                this.logout();
            }
        }
    }

    // 加载用户数据
    loadUsers() {
        const savedUsers = localStorage.getItem('medicalMapUsers');
        if (savedUsers) {
            try {
                return JSON.parse(savedUsers);
            } catch (error) {
                console.error('解析用户数据失败:', error);
                return [];
            }
        }
        return [];
    }

    // 保存用户数据
    saveUsers() {
        localStorage.setItem('medicalMapUsers', JSON.stringify(this.users));
    }

    // 验证邮箱格式
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // 验证密码强度
    validatePassword(password) {
        return password.length >= 6;
    }

    // 用户注册
    async register(userData) {
        console.log('AuthManager.register 被调用，数据:', userData);
        const { email, username, password, confirmPassword, userType } = userData;

        // 验证输入
        if (!email || !username || !password || !confirmPassword || !userType) {
            console.log('验证失败：缺少必填字段');
            throw new Error('请填写所有必填字段');
        }

        // 如果启用了数据库API，使用API注册
        if (this.useDatabase) {
            return await this.registerWithAPI(userData);
        }

        if (!this.validateEmail(email)) {
            console.log('邮箱验证失败:', email);
            throw new Error('请输入有效的邮箱地址');
        }

        if (!this.validatePassword(password)) {
            console.log('密码验证失败:', password);
            throw new Error('密码至少需要6位字符');
        }

        if (password !== confirmPassword) {
            console.log('密码确认失败');
            throw new Error('两次输入的密码不一致');
        }

        // 检查邮箱是否已存在
        if (this.users.find(user => user.email === email)) {
            throw new Error('该邮箱已被注册');
        }

        // 检查用户名是否已存在
        if (this.users.find(user => user.username === username)) {
            throw new Error('该用户名已被使用');
        }

        // 创建新用户
        const newUser = {
            id: Date.now().toString(),
            email,
            username,
            password: this.hashPassword(password), // 在实际应用中应使用更安全的哈希算法
            userType,
            avatar: this.generateAvatar(username),
            createdAt: new Date().toISOString(),
            profile: {
                bio: '',
                location: '',
                specialties: [],
                experience: '',
                verified: false
            },
            preferences: {
                language: 'zh-CN',
                notifications: true,
                privacy: 'public'
            },
            stats: {
                questionsAsked: 0,
                answersGiven: 0,
                helpfulVotes: 0,
                loginCount: 0
            }
        };

        this.users.push(newUser);
        this.saveUsers();

        return { success: true, message: '注册成功！请登录您的账户。' };
    }

    // 使用API注册用户
    async registerWithAPI(userData) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    username: userData.username,
                    email: userData.email,
                    password: userData.password,
                    userType: userData.userType,
                    realName: userData.realName
                })
            });

            const result = await response.json();

            if (result.success) {
                console.log('API注册成功:', result);
                return { success: true, message: result.message };
            } else {
                console.log('API注册失败:', result.message);
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('API注册请求失败:', error);
            // 如果API失败，回退到本地存储
            return await this.registerLocally(userData);
        }
    }

    // 本地注册（原有逻辑的备份）
    async registerLocally(userData) {
        // 这里保留原有的本地注册逻辑作为备份
        console.log('使用本地存储注册，API不可用');

        const { email, username, password, confirmPassword, userType } = userData;

        if (!this.validateEmail(email)) {
            throw new Error('请输入有效的邮箱地址');
        }

        if (!this.validatePassword(password)) {
            throw new Error('密码至少需要6位字符');
        }

        if (password !== confirmPassword) {
            throw new Error('两次输入的密码不一致');
        }

        // 检查用户名和邮箱是否已存在
        const existingUser = this.users.find(user =>
            user.username === username || user.email === email
        );

        if (existingUser) {
            throw new Error('用户名或邮箱已存在');
        }

        // 创建新用户（简化版本）
        const newUser = {
            id: Date.now(),
            uuid: this.generateUUID(),
            username,
            email,
            password: this.hashPassword(password),
            userType,
            createdAt: new Date().toISOString(),
            isActive: true,
            avatar: {
                type: 'initial',
                color: this.generateAvatarColor(),
                text: username.charAt(0).toUpperCase()
            }
        };

        this.users.push(newUser);
        this.saveUsers();

        return { success: true, message: '注册成功（本地存储）' };
    }

    // 用户登录
    async login(email, password, remember = false) {
        if (!email || !password) {
            return { success: false, message: '请输入邮箱和密码' };
        }

        // 优先使用API登录
        if (this.useDatabase) {
            try {
                const result = await this.loginWithAPI(email, password, remember);
                if (result.success) {
                    return result;
                }
                // API失败，继续使用本地存储
                console.log('API登录失败，尝试本地存储:', result.message);
            } catch (error) {
                console.error('API登录出错，使用本地存储:', error);
            }
        }

        // 本地存储登录（备用方案）
        return this.loginLocally(email, password, remember);
    }

    // 使用API登录
    async loginWithAPI(email, password, remember = false) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            });

            const result = await response.json();

            if (result.success) {
                // 设置当前用户
                this.currentUser = result.user;
                this.isLoggedIn = true;

                // 保存会话
                const sessionData = {
                    user: this.currentUser,
                    expiry: Date.now() + (remember ? this.sessionTimeout * 7 : this.sessionTimeout)
                };
                localStorage.setItem('medicalMapSession', JSON.stringify(sessionData));

                // 更新聊天功能的用户状态
                if (window.chatManager) {
                    window.chatManager.updateUser(this.currentUser);
                }

                console.log('API登录成功:', result.user);
                return {
                    success: true,
                    message: '登录成功！',
                    user: this.currentUser,
                    redirect: 'index.html'
                };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('API登录请求失败:', error);
            throw error;
        }
    }

    // 本地存储登录（备用方案）
    loginLocally(email, password, remember = false) {
        // 查找用户
        const user = this.users.find(u => u.email === email);
        if (!user) {
            return { success: false, message: '用户不存在（本地存储）' };
        }

        // 验证密码
        if (!this.verifyPassword(password, user.password)) {
            return { success: false, message: '密码错误' };
        }

        // 更新登录统计
        user.stats.loginCount++;
        user.lastLoginAt = new Date().toISOString();
        this.saveUsers();

        // 设置当前用户
        this.currentUser = { ...user };
        delete this.currentUser.password; // 从内存中移除密码
        this.isLoggedIn = true;

        // 保存会话
        const sessionData = {
            user: this.currentUser,
            expiry: Date.now() + (remember ? this.sessionTimeout * 7 : this.sessionTimeout)
        };
        localStorage.setItem('medicalMapSession', JSON.stringify(sessionData));

        // 更新聊天功能的用户状态
        if (window.chatManager) {
            window.chatManager.updateUser(this.currentUser);
        }

        return {
            success: true,
            message: '登录成功！（本地存储）',
            user: this.currentUser,
            redirect: 'index.html'
        };
    }

    // 用户登出
    logout() {
        this.currentUser = null;
        this.isLoggedIn = false;
        localStorage.removeItem('medicalMapSession');

        // 清除聊天功能的用户状态
        if (window.chatManager) {
            window.chatManager.clearUser();
        }

        // 清除页面会话存储
        sessionStorage.clear();

        // 跳转到登录页面
        window.location.href = 'login.html';
    }

    // 获取当前用户
    getCurrentUser() {
        return this.currentUser;
    }

    // 检查是否已登录
    isAuthenticated() {
        return this.isLoggedIn;
    }

    // 更新用户资料
    async updateProfile(updates) {
        if (!this.isLoggedIn) {
            throw new Error('请先登录');
        }

        const userIndex = this.users.findIndex(u => u.id === this.currentUser.id);
        if (userIndex === -1) {
            throw new Error('用户不存在');
        }

        // 更新用户数据
        this.users[userIndex] = { ...this.users[userIndex], ...updates };
        this.currentUser = { ...this.users[userIndex] };
        delete this.currentUser.password;

        this.saveUsers();

        // 更新会话数据
        const savedSession = localStorage.getItem('medicalMapSession');
        if (savedSession) {
            const session = JSON.parse(savedSession);
            session.user = this.currentUser;
            localStorage.setItem('medicalMapSession', JSON.stringify(session));
        }

        return { success: true, message: '资料更新成功' };
    }

    // 更改密码
    async changePassword(oldPassword, newPassword) {
        if (!this.isLoggedIn) {
            throw new Error('请先登录');
        }

        const user = this.users.find(u => u.id === this.currentUser.id);
        if (!user) {
            throw new Error('用户不存在');
        }

        if (!this.verifyPassword(oldPassword, user.password)) {
            throw new Error('原密码错误');
        }

        if (!this.validatePassword(newPassword)) {
            throw new Error('新密码至少需要6位字符');
        }

        user.password = this.hashPassword(newPassword);
        this.saveUsers();

        return { success: true, message: '密码修改成功' };
    }

    // 获取用户列表（管理员功能）
    getUsers() {
        if (!this.isLoggedIn || this.currentUser.userType !== 'admin') {
            throw new Error('无权限访问');
        }
        return this.users.map(user => ({ ...user, password: undefined }));
    }

    // 简单的密码哈希（在生产环境中应使用更安全的方法）
    hashPassword(password) {
        // 这里使用简单的哈希，生产环境应使用bcrypt等安全库
        return btoa(password + 'medical_map_salt');
    }

    // 验证密码
    verifyPassword(password, hash) {
        return this.hashPassword(password) === hash;
    }

    // 生成头像
    generateAvatar(username) {
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
        const color = colors[username.length % colors.length];
        const initial = username.charAt(0).toUpperCase();
        
        return {
            type: 'initial',
            color: color,
            text: initial
        };
    }

    // 社交登录（模拟）
    async socialLogin(provider) {
        // 模拟社交登录
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockUsers = {
            wechat: {
                id: 'wechat_' + Date.now(),
                email: '<EMAIL>',
                username: '微信用户',
                userType: 'patient',
                provider: 'wechat'
            },
            qq: {
                id: 'qq_' + Date.now(),
                email: '<EMAIL>',
                username: 'QQ用户',
                userType: 'patient',
                provider: 'qq'
            }
        };

        const userData = mockUsers[provider];
        if (!userData) {
            throw new Error('不支持的登录方式');
        }

        // 检查是否已存在该社交账号
        let user = this.users.find(u => u.email === userData.email);
        if (!user) {
            // 创建新用户
            user = {
                ...userData,
                avatar: this.generateAvatar(userData.username),
                createdAt: new Date().toISOString(),
                profile: {
                    bio: '',
                    location: '',
                    specialties: [],
                    experience: '',
                    verified: false
                },
                preferences: {
                    language: 'zh-CN',
                    notifications: true,
                    privacy: 'public'
                },
                stats: {
                    questionsAsked: 0,
                    answersGiven: 0,
                    helpfulVotes: 0,
                    loginCount: 1
                }
            };
            this.users.push(user);
        } else {
            user.stats.loginCount++;
        }

        user.lastLoginAt = new Date().toISOString();
        this.saveUsers();

        this.currentUser = { ...user };
        this.isLoggedIn = true;

        // 保存会话
        const sessionData = {
            user: this.currentUser,
            expiry: Date.now() + this.sessionTimeout
        };
        localStorage.setItem('medicalMapSession', JSON.stringify(sessionData));

        return { 
            success: true, 
            message: `${provider === 'wechat' ? '微信' : 'QQ'}登录成功！`, 
            user: this.currentUser,
            redirect: 'index.html'
        };
    }

    // 权限检查
    hasPermission(permission) {
        if (!this.isLoggedIn) return false;
        
        const permissions = {
            'patient': ['view_hospitals', 'ask_questions', 'rate_hospitals'],
            'doctor': ['view_hospitals', 'ask_questions', 'answer_questions', 'manage_profile'],
            'researcher': ['view_hospitals', 'ask_questions', 'view_analytics', 'export_data'],
            'admin': ['*'] // 管理员拥有所有权限
        };

        const userPermissions = permissions[this.currentUser.userType] || [];
        return userPermissions.includes('*') || userPermissions.includes(permission);
    }
}

// 导出模块
window.AuthManager = AuthManager;