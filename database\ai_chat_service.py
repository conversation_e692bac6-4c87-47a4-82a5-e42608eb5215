"""
AI聊天服务配置和接口
支持gemini-2.0-flash模型
"""
import os
import json
import requests
from datetime import datetime

class AIConfigManager:
    """AI配置管理器"""
    
    def __init__(self, config_file='config.txt'):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """加载AI配置"""
        config = {
            'base_url': 'http://*************:3000/v1',
            'api_key': 'sk-3BVZyBBs64JYc8R1334eIXVSivrjs8kVKT9q4VGw75L23aW3',
            'model': 'gemini-2.0-flash'
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines:
                        line = line.strip()
                        if ':' in line and not line.startswith('#'):
                            key, value = line.split(':', 1)
                            if key == 'base_url':
                                config['base_url'] = value
                            elif key == 'api':
                                config['api_key'] = value
                            elif key == 'model':
                                config['model'] = value
        except Exception as e:
            print(f"加载AI配置失败: {e}")
        
        return config

class AIChatService:
    """AI聊天服务"""
    
    def __init__(self, config_manager):
        self.config = config_manager.config
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.config["api_key"]}'
        })
    
    def chat_with_ai(self, message, conversation_history=None):
        """与AI聊天（同步版本）"""
        try:
            # 构建消息历史
            messages = []
            
            # 系统提示词
            system_prompt = """你是医疗专长地图平台的AI助手，名叫"医疗小助手"。你的主要职责是：

1. 回答用户关于医疗专长和医院信息的问题
2. 提供医疗健康相关的建议和信息
3. 帮助用户理解平台功能
4. 保持友好、专业的服务态度

注意事项：
- 不能提供具体的医疗诊断
- 建议用户如有严重症状及时就医
- 回答要简洁明了，控制在200字以内
- 使用中文回答
"""
            
            messages.append({
                "role": "system",
                "content": system_prompt
            })
            
            # 添加对话历史（最多保留最近5轮对话）
            if conversation_history:
                for item in conversation_history[-10:]:  # 最多10条历史消息
                    if item.get('username') != '医疗小助手':
                        messages.append({
                            "role": "user", 
                            "content": item.get('message', '')
                        })
                    else:
                        messages.append({
                            "role": "assistant",
                            "content": item.get('message', '')
                        })
            
            # 添加当前用户消息
            messages.append({
                "role": "user",
                "content": message
            })
            
            # 调用AI API
            response = self._call_ai_api(messages)
            return response
            
        except Exception as e:
            print(f"AI聊天失败: {e}")
            return "抱歉，AI助手暂时无法回应，请稍后再试。"
    
    def _call_ai_api(self, messages):
        """调用AI API（同步版本）"""
        try:
            payload = {
                "model": self.config['model'],
                "messages": messages,
                "max_tokens": 300,
                "temperature": 0.7,
                "stream": False
            }
            
            response = self.session.post(
                f"{self.config['base_url']}/chat/completions",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content'].strip()
                else:
                    return "AI响应格式异常，请稍后再试。"
            else:
                print(f"AI API错误: {response.status_code} - {response.text}")
                return "AI服务暂时不可用，请稍后再试。"
                
        except requests.exceptions.Timeout:
            return "AI响应超时，请稍后再试。"
        except Exception as e:
            print(f"调用AI API异常: {e}")
            return "AI服务异常，请稍后再试。"
    
    def is_ai_trigger(self, message):
        """判断是否触发AI回复"""
        ai_triggers = [
            '@ai', '@AI', '@医疗小助手', '@助手',
            '小助手', 'AI', 'ai', '人工智能',
            '问医生', '咨询', '询问'
        ]
        
        message_lower = message.lower()
        return any(trigger.lower() in message_lower for trigger in ai_triggers)

# 全局AI服务实例
ai_config_manager = AIConfigManager()
ai_chat_service = AIChatService(ai_config_manager)