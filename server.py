#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地域医疗专长地图 - 本地开发服务器
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import signal
import threading
import time
from urllib.parse import unquote
import subprocess
import json

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def do_GET(self):
        """处理GET请求，实现登录重定向"""
        # 根路径重定向到登录页面
        if self.path == '/':
            self.send_response(302)
            self.send_header('Location', '/login.html')
            self.end_headers()
            self.log_message("Redirect to login page")
            return
        
        # 调用父类的默认处理
        super().do_GET()
    
    def end_headers(self):
        # 添加CORS头部
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.end_headers()
    
    def guess_type(self, path):
        """改进的MIME类型猜测"""
        mimetype = super().guess_type(path)
        
        # 确保CSV文件正确的MIME类型
        if path.lower().endswith('.csv'):
            return 'text/csv'
        elif path.lower().endswith('.html'):
            return 'text/html; charset=utf-8'
        elif path.lower().endswith('.js'):
            return 'application/javascript; charset=utf-8'
        elif path.lower().endswith('.css'):
            return 'text/css; charset=utf-8'
        
        return mimetype
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        client_ip = self.client_address[0]
        print(f"[{timestamp}] {client_ip} - {format % args}")

class MedicalMapServer:
    """医疗地图服务器类"""

    def __init__(self, port=8000, host='localhost', api_port=5000):
        self.port = port
        self.host = host
        self.api_port = api_port
        self.httpd = None
        self.server_thread = None
        self.api_process = None
        self.enable_api = True
        
    def check_files(self):
        """检查必需的文件是否存在"""
        required_files = ['index.html', '1.csv']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print("❌ 缺少必需的文件:")
            for file in missing_files:
                print(f"   - {file}")
            print("\n请确保以下文件在当前目录中:")
            print("   - index.html (主页面文件)")
            print("   - 1.csv (医院数据文件)")
            return False
        
        print("✅ 文件检查通过")
        return True

    def check_api_requirements(self):
        """检查API服务器的依赖"""
        if not self.enable_api:
            return True

        # 检查database目录和文件
        if not os.path.exists('database'):
            print("⚠️  database目录不存在，将禁用API功能")
            self.enable_api = False
            return True

        api_files = ['database/api_server.py']
        missing_files = []

        for file in api_files:
            if not os.path.exists(file):
                missing_files.append(file)

        if missing_files:
            print("⚠️  缺少API相关文件，将禁用API功能:")
            for file in missing_files:
                print(f"   - {file}")
            self.enable_api = False
            return True

        # 检查Python依赖
        try:
            import flask
            print("✅ Flask依赖检查通过")

            # 检查数据库配置
            if self.setup_database_config():
                print("✅ 数据库配置完成")
                return True
            else:
                print("⚠️  数据库配置失败，将禁用API功能")
                self.enable_api = False
                return True

        except ImportError as e:
            print(f"⚠️  缺少API依赖包，将禁用API功能: {e}")
            print("   请运行: pip install flask flask-cors")
            self.enable_api = False
            return True

    def setup_database_config(self):
        """设置数据库配置"""
        config_file = 'database/config.py'
        sqlite_config = 'database/config_sqlite.py'
        mysql_backup = 'database/config_mysql_backup.py'

        # 如果SQLite数据库存在，使用SQLite配置
        sqlite_db = 'database/medical_map.db'
        if os.path.exists(sqlite_db):
            print("🗄️  检测到SQLite数据库，使用SQLite配置")
            if os.path.exists(sqlite_config):
                # 备份MySQL配置
                if os.path.exists(config_file) and not os.path.exists(mysql_backup):
                    try:
                        import shutil
                        shutil.copy2(config_file, mysql_backup)
                        print(f"📦 已备份MySQL配置到: {mysql_backup}")
                    except:
                        pass

                # 使用SQLite配置
                try:
                    import shutil
                    shutil.copy2(sqlite_config, config_file)
                    print("✅ 已切换到SQLite配置")
                    return True
                except Exception as e:
                    print(f"❌ 切换SQLite配置失败: {e}")
                    return False

        # 检查MySQL配置
        if os.path.exists(config_file):
            try:
                # 尝试导入配置文件
                sys.path.insert(0, 'database')
                print("✅ 数据库配置文件存在")
                return True
            except Exception as e:
                print(f"⚠️  数据库配置错误: {e}")
                return False

        print("❌ 未找到有效的数据库配置")
        return False
    
    def find_available_port(self):
        """寻找可用的端口"""
        import socket
        
        for port in range(self.port, self.port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind((self.host, port))
                    self.port = port
                    return True
            except OSError:
                continue
        
        print(f"❌ 无法找到可用端口 ({self.port}-{self.port + 100})")
        return False

    def start_api_server(self):
        """启动API服务器"""
        if not self.enable_api:
            return True

        try:
            # 检查API端口是否可用
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                result = s.connect_ex((self.host, self.api_port))
                if result == 0:
                    print(f"⚠️  API端口 {self.api_port} 已被占用，尝试其他端口...")
                    for port in range(self.api_port + 1, self.api_port + 10):
                        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s2:
                            if s2.connect_ex((self.host, port)) != 0:
                                self.api_port = port
                                break
                    else:
                        print("❌ 无法找到可用的API端口")
                        self.enable_api = False
                        return True

            # 启动API服务器进程
            api_env = os.environ.copy()
            api_env['API_PORT'] = str(self.api_port)
            api_env['PYTHONPATH'] = os.getcwd()  # 设置Python路径

            print(f"🔄 启动API服务器 (端口: {self.api_port})...")
            
            self.api_process = subprocess.Popen([
                sys.executable, 'database/api_server.py'
            ], env=api_env, cwd=os.getcwd(), 
               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # 等待API服务器启动
            time.sleep(3)  # 增加等待时间

            # 检查API服务器是否成功启动
            if self.api_process.poll() is None:
                print(f"🚀 API服务器启动成功!")
                print(f"📡 API地址: http://{self.host}:{self.api_port}/api")
                return True
            else:
                stdout, stderr = self.api_process.communicate()
                print(f"❌ API服务器启动失败:")
                if stdout:
                    print(f"   输出: {stdout.decode('utf-8', errors='ignore')}")
                if stderr:
                    print(f"   错误: {stderr.decode('utf-8', errors='ignore')}")
                print("💡 尝试手动启动: python start_api.py")
                self.enable_api = False
                return True

        except Exception as e:
            print(f"❌ 启动API服务器异常: {e}")
            self.enable_api = False
            return True
    
    def start_server(self):
        """启动HTTP服务器和API服务器"""
        # 先启动API服务器
        if not self.start_api_server():
            return False

        try:
            self.httpd = socketserver.TCPServer((self.host, self.port), CORSHTTPRequestHandler)
            self.httpd.timeout = 1  # 设置超时以支持优雅关闭

            print(f"🚀 Web服务器启动成功!")
            print(f"📡 Web地址: http://{self.host}:{self.port}")
            print(f"📁 目录: {os.getcwd()}")
            print("🔗 登录入口: http://{}:{}/".format(self.host, self.port))

            if self.enable_api:
                print(f"🔌 API服务: http://{self.host}:{self.api_port}/api")
                print("💬 聊天功能: 已启用")
                print("🗄️  数据库功能: 已启用")
            else:
                print("⚠️  API服务: 已禁用 (使用本地存储)")

            print("\n⌨️  按 Ctrl+C 停止所有服务器")
            print("-" * 60)

            # 在新线程中运行服务器
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()

            return True

        except OSError as e:
            if "Address already in use" in str(e):
                print(f"❌ 端口 {self.port} 已被占用，尝试寻找其他端口...")
                if self.find_available_port():
                    return self.start_server()
            print(f"❌ 启动Web服务器失败: {e}")
            return False
    
    def _run_server(self):
        """在线程中运行服务器"""
        try:
            while True:
                self.httpd.handle_request()
        except Exception as e:
            if "Server stopped" not in str(e):
                print(f"❌ 服务器运行错误: {e}")
    
    def open_browser(self, delay=1.5):
        """延迟打开浏览器"""
        def _open():
            time.sleep(delay)
            url = f"http://{self.host}:{self.port}/"
            print(f"🌐 正在打开浏览器: {url}")
            try:
                webbrowser.open(url)
            except Exception as e:
                print(f"❌ 无法自动打开浏览器: {e}")
                print(f"   请手动访问: {url}")
        
        threading.Thread(target=_open, daemon=True).start()
    
    def stop_server(self):
        """停止所有服务器"""
        print("\n🛑 正在停止所有服务器...")

        # 停止API服务器
        if self.api_process:
            try:
                self.api_process.terminate()
                # 等待进程结束
                try:
                    self.api_process.wait(timeout=5)
                    print("✅ API服务器已停止")
                except subprocess.TimeoutExpired:
                    self.api_process.kill()
                    print("⚠️  强制终止API服务器")
            except Exception as e:
                print(f"⚠️  停止API服务器时出错: {e}")

        # 停止Web服务器
        if self.httpd:
            self.httpd.shutdown()
            self.httpd.server_close()
            print("✅ Web服务器已停止")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n📡 收到信号 {signum}，正在优雅关闭...")
    if hasattr(signal_handler, 'server'):
        signal_handler.server.stop_server()
    sys.exit(0)

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                   🏥 地域医疗专长地图                        ║
║              Medical Specialty Map Integrated Server        ║
║                                                              ║
║  一个基于地理位置的智慧医疗专长匹配平台                      ║
║  💬 集成Web服务器 + API服务器 + 实时聊天功能                 ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_help():
    """打印帮助信息"""
    help_text = """
📖 使用说明:
   python server.py [Web端口号] [API端口号]

🔧 参数:
   Web端口号    可选，默认为8000 (网页服务器端口)
   API端口号    可选，默认为5000 (数据库API端口)

📋 例子:
   python server.py           # 使用默认端口 Web:8000, API:5000
   python server.py 3000      # Web端口3000, API端口5000
   python server.py 3000 6000 # Web端口3000, API端口6000

📁 文件要求:
   - index.html: 主页面文件
   - 1.csv:  医院数据文件

🌐 访问地址:
   启动后会自动打开浏览器，或手动访问 http://localhost:[端口]/

⚠️  注意事项:
   - 请确保端口未被占用
   - 建议使用Chrome或Firefox浏览器
   - 需要Google Maps API密钥才能显示地图
   - 首次访问会重定向到登录页面
    """
    print(help_text)

def main():
    """主函数"""
    print_banner()
    
    # 解析命令行参数
    port = 8000
    api_port = 5000

    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help', 'help']:
            print_help()
            return
        try:
            port = int(sys.argv[1])
            if port < 1 or port > 65535:
                raise ValueError("Web端口号必须在1-65535之间")
        except ValueError as e:
            print(f"❌ 无效的Web端口号: {e}")
            print("   使用 'python server.py --help' 查看帮助")
            return

    if len(sys.argv) > 2:
        try:
            api_port = int(sys.argv[2])
            if api_port < 1 or api_port > 65535:
                raise ValueError("API端口号必须在1-65535之间")
        except ValueError as e:
            print(f"❌ 无效的API端口号: {e}")
            print("   使用 'python server.py --help' 查看帮助")
            return

    # 创建服务器实例
    server = MedicalMapServer(port=port, api_port=api_port)
    
    # 设置信号处理器
    signal_handler.server = server
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查文件和依赖
    if not server.check_files():
        print("\n💡 提示: 请确保在项目根目录中运行此脚本")
        return

    # 检查API依赖
    server.check_api_requirements()

    # 启动服务器
    if server.start_server():
        # 自动打开浏览器
        server.open_browser()
        
        try:
            # 保持主线程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            signal_handler(signal.SIGINT, None)
    else:
        print("❌ 服务器启动失败")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code or 0)
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1) 